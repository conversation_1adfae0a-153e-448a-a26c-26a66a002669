# PRD-13: 数据中心 (Data Center & BI)

## 1. 背景 (Background)

随着公司各业务模块（销售、采购、生产、仓储、财务、项目、人事等）的全面信息化，系统将沉淀海量的业务数据。这些数据是公司最宝贵的资产之一，但如果它们分散在各个独立的子系统中，其价值将大打折扣。

为了充分利用这些数据，赋能管理层进行科学决策，需要构建一个统一的数据中心。该中心负责将来自不同业务系统的数据进行抽取、整合、清洗和分析，并以直观的可视化方式（如报表、驾驶舱）呈现给各级管理者。其最终目标是提供一个“上帝视角”的业务全景图，实时洞察经营状况，发现问题，预测趋势。

## 2. 用户画像 (Personas)

| 画像 (Persona) | 角色 (Role) | 核心需求 (Key Needs) |
| :--- | :--- | :--- |
| 高总 | 公司总经理 (General Manager) | - 一眼看清公司整体经营状况（收入、成本、利润）。<br>- 实时监控核心KPI，如销售额、订单完成率、资金周转率。<br>- 快速钻取，定位异常数据背后的原因。<br>- 获得跨部门（如销售与生产）的综合洞察。 |
| 王主管 | 生产总监 (Production Director) | - 监控各产线的生产效率、设备利用率、在制品（WIP）情况。<br>- 分析生产成本构成，特别是料、工、费。<br>- 跟踪订单的准时交付率（OTD）和产品合格率。 |
| 赵经理 | 销售总监 (Sales Director) | - 查看销售团队和个人的业绩排名。<br>- 分析不同产品线、不同区域的销售额和利润贡献。<br>- 跟踪销售漏斗的转化率和客户的生命周期价值。 |
| 陈会计 | 财务经理 (Finance Manager) | - 获取标准的三大财务报表（利润表、资产负债表、现金流量表）。<br>- 进行深入的成本分析和预算执行分析。<br>- 监控应收账款和应付账款的账龄。 |

## 3. 功能需求 (Functional Requirements)

### 3.1 数据集成与处理 (ETL)
- **FR3.1.1 数据源连接**: 系统需能连接并抽取所有内部业务子系统（如销售、生产、财务等）的数据库。
- **FR3.1.2 数据抽取 (Extract)**: 支持定时（如每小时、每日）从源系统抽取增量数据。
- **FR3.1.3 数据转换与清洗 (Transform & Clean)**:
    - 将不同系统的数据格式进行统一（如日期、编码）。
    - 清洗重复、错误或不完整的数据。
    - 按照预设的业务规则进行数据计算和汇总，形成分析主题（如销售主题、成本主题）。
- **FR3.1.4 数据加载 (Load)**: 将处理后的高质量数据加载到数据仓库或数据集市中，供前端分析使用。

### 3.2 可视化驾驶舱 (Dashboard)
- **FR3.2.1 总经理驾驶舱 (GM's Cockpit)**:
    - **核心财务指标**: 实时销售额、毛利、净利润、回款率。
    - **核心运营指标**: 订单总数、准时交付率、总产量、库存周转天数。
    - **趋势分析**: 过去12个月的销售额与利润趋势图。
    - **预警模块**: 关键指标低于预设阈值时高亮预警（如毛利率低于20%）。
- **FR3.2.2 销售主题驾驶舱**:
    - 销售业绩地图、产品线销售分析、客户贡献度分析（ABC分析）、销售漏斗分析。
- **FR3.2.3 生产主题驾驶舱**:
    - 产线OEE（设备综合效率）、工单达成率、在制品监控、工序合格率分析。
- **FR3.2.4 财务主题驾驶舱**:
    - 动态财务报表、成本构成分析（按产品/订单）、账龄分析、预算与实际对比。
- **FR3.2.5 项目主题驾驶舱**:
    - 项目进度（WBS）概览、项目成本与预算对比、项目资源负载。

### 3.3 自定义报表与分析
- **FR3.3.1 拖拽式报表设计**: 允许有权限的用户（如数据分析师）通过拖拽字段的方式，创建新的报表和图表。
- **FR3.3.2 数据钻取与切片**: 用户可以在图表上进行下钻（如从年数据钻取到月数据）、上卷、切片（如只看华东区的数据）等交互式分析。
- **FR3.3.3 订阅与导出**: 用户可以订阅自己关注的报表，系统会定期（如每日、每周）将最新报表通过邮件发送给用户。支持将报表导出为Excel或PDF格式。

## 4. 非功能需求 (Non-Functional Requirements)

- **NF4.1 性能**: 驾驶舱核心指标的加载时间应在5秒以内。ETL过程不应影响源业务系统的性能。
- **NF4.2 数据一致性**: 必须确保数据中心的数据与源业务系统在抽取时间点的数据是完全一致的。
- **NF4.3 可靠性**: ETL任务需有失败重试和异常监控机制。
- **NF4.4 扩展性**: 数据模型和ETL流程应易于扩展，以便未来接入新的数据源或增加新的分析主题。
- **NF4.5 安全性**: 严格控制数据访问权限，不同角色只能看到其权限范围内的数据（如销售经理只能看到自己团队的数据）。

## 5. 总体架构图 (Architecture)

```mermaid
graph TD
    subgraph "数据源 (Data Sources)"
        A1[销售系统DB]
        A2[生产系统DB]
        A3[财务系统DB]
        A4[...]
    end

    subgraph "数据中心 (Data Center)"
        B[ETL服务器]
        C[数据仓库 (DWH)]
        D[分析引擎 (OLAP)]

        A1 --> B
        A2 --> B
        A3 --> B
        A4 --> B
        B --> C
        C --> D
    end

    subgraph "数据应用 (Data Application)"
        E[可视化服务器]
        F[总经理驾驶舱]
        G[各主题驾驶舱]
        H[自定义报表]

        D --> E
        E --> F
        E --> G
        E --> H
    end

    subgraph "用户 (Users)"
        I[PC/Mac]
        J[移动设备]

        I --> F & G & H
        J --> F & G & H
    end
```

## 6. 验收标准 (Acceptance Criteria)

- **AC6.1**: 总经理驾驶舱能正确显示与财务系统中利润表一致的上月总销售额和总利润。
- **AC6.2**: 在总经理驾驶舱点击“总销售额”卡片，可以下钻到各个产品线的销售额分布图。
- **AC6.3**: 生产总监可以在生产驾驶舱中，查看到昨天“切割产线”的OEE为85%。
- **AC6.4**: 数据分析师可以通过拖拽“客户名称”和“订单金额”字段，成功创建一个“客户销售额排名”的自定义报表。
- **AC6.5**: 销售总监订阅“每周销售业绩报告”后，能在下周一早上9点收到包含最新数据的邮件。
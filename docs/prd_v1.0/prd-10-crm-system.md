# PRD-10: 客户关系管理 (CRM) 子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: Roo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
客户是企业最宝贵的资产。在竞争激烈的市场中，如何系统化地管理客户信息、追踪销售机会、提供优质服务，是决定企业能否持续增长的关键。目前，销售团队可能依赖Excel或个人笔记来管理客户，导致信息零散、跟进不及时、团队协作困难、销售过程无法有效管理。本子系统（CRM）旨在为销售和服务团队提供一套专业的管理工具，实现客户全生命周期的精细化管理。

### 1.2 商业价值
- **提升销售转化率**: 通过对销售机会的阶段化管理和及时的跟进提醒，帮助销售团队抓住每一个潜在订单。
- **沉淀客户资产**: 将分散在销售人员手中的客户信息，统一沉淀为公司级的数字资产，避免因人员流动造成的客户流失。
- **改善客户关系**: 建立360度的客户视图，让每一位接触客户的员工都能全面了解客户，提供更个性化、更专业的服务。
- **销售过程可视化**: 为销售管理者提供销售漏斗、业绩预测等分析工具，使其能清晰掌握团队状态，科学决策。

### 1.3 关键目标
- **目标1**: 建立统一的客户信息库，实现对客户档案、联系人、历史互动记录的全面管理。
- **目标2**: 实现对销售机会从线索到赢单的全过程追踪与管理。
- **目标3**: 为销售人员提供高效的日常工作工具，如活动记录、任务提醒等。
- **目标4**: 建立售后服务管理流程，确保客户问题能够得到及时响应和有效解决。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 小张
- **角色**: **一线销售人员**
- **背景**: 负责区域内的客户开发和关系维护，同时跟进多个销售机会。日常工作繁忙，需要在办公室和客户现场之间频繁切换。
- **核心诉求**:
    - "我希望能有一个系统，能随时提醒我今天要联系哪些客户，跟进哪些机会，别让我忘了事。"
    - "每次去拜访客户前，我希望能快速看到我们和这个客户所有的历史交往记录，包括他买过什么，提过什么问题。"
    - "我希望能方便地记录每次拜访的内容，并设置下次回访的提醒。"

### 2.2 核心场景
- **场景一：跟进一个销售机会**
  1.  **触发**: 销售小张通过市场活动获得一个潜在客户的线索。
  2.  **操作路径**:
      - 小张在CRM系统中新建一个“客户”和一个关联的“销售机会”。
      - 他将机会的初始阶段设置为“初步接洽”，并录入预计成交金额和产品意向。
      - 他在系统中新建一个“跟进活动”，记录了与客户的第一次电话沟通内容，并创建了一个“任务”，提醒自己下周三前给客户发送产品资料。
      - 下周三，系统自动提醒小张完成任务。他发送资料后，将任务标记为完成，并更新了销售机会的阶段为“需求分析”。
  3.  **系统响应**:
      - 销售机会在销售漏斗中的位置向前推进。
      - 销售经理可以在团队看板上看到这个新机会的进展。
  4.  **期望**: 销售过程的每一步都有迹可循，系统能成为销售人员的智能助手。

- **场景二：360°客户视图**
  1.  **触发**: 小张即将拜访一个重要老客户“ABC建筑公司”。
  2.  **操作路径**:
      - 他在CRM中打开“ABC建筑公司”的客户详情页。
  3.  **系统响应**:
      - 页面上清晰地展示：
          - **基本信息**: 客户的地址、电话、规模等。
          - **联系人**: 项目经理、采购经理等关键联系人的联系方式。
          - **历史跟进记录**: 公司所有销售人员与该客户的历史沟通记录。
          - **历史交易**: 该客户过去所有已成交的销售订单列表（与ERP销售模块打通）。
          - **历史服务记录**: 该客户过去所有的售后服务请求和处理结果。
  4.  **期望**: 在一个页面就能全面了解客户，为拜访做好充分准备，体现公司的专业性。

- **场景三：处理一个客户售后请求**
  1.  **触发**: 客户来电反映之前安装的玻璃有划痕。
  2.  **操作路径**:
      - 客服人员在CRM中新建一个“服务工单”。
      - 录入客户信息、问题描述，并将工单指派给售后工程师。
      - 售后工程师接收工单，联系客户，上门查看，并在系统中更新处理进展。
      - 问题解决后，工程师在系统中关闭工单，并填写解决方案。
  3.  **期望**: 客户的每一个问题都能被记录、被指派、被追踪，直到最终解决，形成服务闭环。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 客户管理 (F-CRM-01)
- **用户故事 (F-CRM-01-01)**: 作为一名**销售人员**，我想要**维护我的客户档案和多联系人信息**，以便**建立完整的客户信息库**。
  - **详细描述**:
    - **正常流程**:
      1. 支持客户（公司）和联系人（个人）的创建、编辑、查询。
      2. 客户信息应包含：客户名称、行业、规模、来源、地址、公海/私池归属等。
      3. 联系人信息应包含：姓名、部门、职位、电话、邮箱等。
      4. 支持客户查重，避免重复录入。
      5. 支持客户的分配（给销售人员）和转移（从公海池领取或在销售间转移）。

### 3.2 销售机会管理 (F-CRM-02)
- **用户故事 (F-CRM-02-01)**: 作为一名**销售人员**，我想要**追踪和管理我的销售机会**，以便**提高赢单率并进行销售预测**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建销售机会，关联客户，并录入机会名称、预计成交金额、预计结单日期、产品意向等。
      2. 支持自定义销售流程阶段（如初步接洽、需求分析、方案报价、商务谈判、赢单/输单）。
      3. 提供可视化的销售漏斗，分析机会在各阶段的转化率。
      4. 销售机会可以直接转化为ERP中的“报价单”。

### 3.3 活动与任务管理 (F-CRM-03)
- **用户故事 (F-CRM-03-01)**: 作为一名**销售人员**，我想要**记录每一次客户跟进行动，并设置待办任务提醒**，以便**高效地管理我的日常工作**。
  - **详细描述**:
    - **正常流程**:
      1. **活动记录**: 支持记录多种类型的客户跟进活动，如电话、拜访、邮件等，并关联到具体的客户或机会。
      2. **任务管理**: 支持创建待办任务，设置到期日和提醒，并将任务分配给同事。
      3. 提供日历视图，清晰展示销售人员的日程安排。

### 3.4 售后服务管理 (F-CRM-04)
- **用户故事 (F-CRM-04-01)**: 作为一名**客服人员**，我想要**统一受理和派发客户的服务请求**，以便**确保客户问题得到及时、有效的解决**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建服务工单（Case），记录客户问题、优先级、问题类型等。
      2. 支持将工单指派给指定的售后工程师或团队。
      3. 支持在工单下记录完整的处理过程和解决方案。
      4. 服务工单可以关联到具体的销售订单或产品。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **移动端适配**: 销售人员经常在外，CRM核心功能（如查看客户、记录跟进）需提供移动端（手机/平板）的良好访问体验。
- **集成性**: 与ERP的销售模块、财务模块需要深度集成。客户主数据应与ERP共享，销售机会能转为报价单，历史订单能反向同步到CRM。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **客户 (Account)**
    - `account_id` (PK)
    - `account_name`
    - `industry`
    - `owner_id` (FK to User, 负责人)
    - ...

2.  **联系人 (Contact)**
    - `contact_id` (PK)
    - `account_id` (FK)
    - `full_name`
    - `title` (职位)
    - `phone`
    - `email`
    - ...

3.  **销售机会 (Opportunity)**
    - `opportunity_id` (PK)
    - `account_id` (FK)
    - `opportunity_name`
    - `amount` (预计金额)
    - `close_date` (预计结单日期)
    - `stage` (销售阶段)
    - ...

4.  **活动 (Activity)**
    - `activity_id` (PK)
    - `subject`
    - `activity_type` (电话, 拜访)
    - `related_to_id` (关联的客户/机会ID)
    - `description`
    - ...

5.  **服务工单 (Case)**
    - `case_id` (PK)
    - `case_number`
    - `account_id` (FK)
    - `subject`
    - `status` (新建, 处理中, 已解决)
    - `owner_id` (FK to User, 处理人)
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 从线索到服务的客户全生命周期
```mermaid
graph TD
    A[市场活动/线索] --> B[销售: 创建客户/机会];
    B --> C[销售: 持续跟进<br/>(记录活动/任务)];
    C --> D{机会成熟?};
    D -- 是 --> E[CRM: 创建报价请求];
    E --> F[ERP-Sales: 创建报价单/订单];
    F --> G[ERP: 订单交付];
    G --> H[CRM: 客户信息中<br/>增加交易历史];
    D -- 否 --> C;
    H --> I{客户发起售后?};
    I -- 是 --> J[客服: 创建服务工单];
    J --> K[售后: 处理并关闭工单];
    K --> H;
    I -- 否 --> C;
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-CRM-01 (360°视图)**:
  - **Given** 我在ERP中为客户A完成了一笔新的销售订单
  - **When** 我在CRM中打开客户A的详情页
  - **Then** 这笔新的销售订单记录必须能实时地、自动地出现在客户A的“历史交易”列表中。

- **AC-CRM-02 (销售机会转化)**:
  - **Given** 我有一个销售机会，预计金额为10万元，当前处于“方案报价”阶段
  - **When** 我点击“转为报价单”
  - **Then** 系统必须能自动跳转到ERP的报价单创建页面，并自动带入客户信息、产品意向和金额。

- **AC-CRM-03 (任务提醒)**:
  - **Given** 我创建了一个任务，要求在今天下午3点前回访客户B
  - **When** 时间到达下午3点
  - **Then** 系统必须通过站内信或桌面通知的方式，向我发出明确的提醒。
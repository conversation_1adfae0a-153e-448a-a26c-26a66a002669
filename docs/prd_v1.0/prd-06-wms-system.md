# PRD-06: 仓储管理子系统 (WMS) 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: Roo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
仓库是企业物料流转的集散中心，其管理效率直接影响资金周转和生产交付。在玻璃深加工行业，物料种类繁多，而成品/半成品（玻璃）又具有易碎、尺寸各异、需特殊载具（铁架/玻璃架）存放的特点，传统的人工记账、按区域堆放的管理方式，极易导致库存数据不准、找货困难、发货错误、空间利用率低等问题。本子系统（WMS）旨在引入现代仓储管理理念，通过信息化和条码化手段，实现对仓库作业全流程的精细化、可视化管理。

### 1.2 商业价值
- **确保账实相符**: 通过扫码出入库，保证系统库存与实物库存的高度一致，为MRP和财务核算提供准确的数据基础。
- **提升作业效率**: 库位管理和扫码操作，能大幅缩短物料上架、拣货、盘点的时间，提升仓库人员的工作效率。
- **优化库存周转**: 准确的库存数据和库龄分析，有助于实施先进先出（FIFO）等库存策略，减少呆滞料，加速资金周转。
- **提高空间利用率**: 通过精细化的库位管理，能更有效地规划和利用有限的仓库空间。

### 1.3 关键目标
- **目标1**: 建立仓库、库区、货架、库位的多级仓储模型，实现对库存物料的精确定位。
- **目标2**: 实现所有核心出入库业务（采购入库、生产入库、销售出库、生产领料）的流程化和无纸化。
- **目标3**: 全面推行条码/二维码管理，为物料批次、库存单元（铁架）生成唯一标识，实现扫码作业。
- **目标4**: 支持库存盘点、调拨等库内管理活动，并提供多维度的库存查询和报表分析。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 小李
- **角色**: **仓库管理员**
- **背景**: 负责仓库的日常收发货和库存管理工作。每天需要接收供应商送来的货物，为生产线准备物料，以及将成品打包发货。
- **核心诉求**:
    - "我希望能用扫码枪‘滴’一下就知道这是什么货，该放到哪个架子上，而不是拿着单子到处找、到处对。"
    - "生产线上要领料，我希望能快速知道料在哪，拿完一扫码，库存就自动扣减了，不用我再回办公室去记账。"
    - "月底盘点太痛苦了，我希望能有工具帮我简化这个过程。"

### 2.2 核心场景
- **场景一：采购收货入库（扫码）**
  1.  **触发**: 供应商将采购的玻璃原片送到仓库。
  2.  **操作路径**:
      - 仓管员小李使用手持终端（PDA），登录WMS。
      - 进入“采购收货”功能，扫描采购订单号。
      - 系统显示该订单的待收货物料列表。
      - 小李逐一核对物料，并输入本次实收数量。
      - 系统为这批原片生成一个唯一的“批次条码”，小李将其打印并贴在物料上。
      - 接着，小李将这批原片放到一个空的玻璃架上，扫描玻璃架上唯一的“库位码”。
  3.  **系统响应**:
      - 系统记录了这批物料的入库信息，并将其与具体的库位码绑定。
      - 采购订单状态更新为“部分收货”或“全部收货”。
      - 该物料的系统库存实时增加。
  4.  **期望**: 整个收货、上架过程无纸化，通过扫码完成数据采集，准确高效。

- **场景二：生产线扫码领料**
  1.  **触发**: 生产订单流转到切割工序，需要领用玻璃原片。
  2.  **操作路径**:
      - 切割工段的工人或仓管员小李，在PDA上调出对应的生产工单。
      - 系统显示该工单需要领用的物料清单（BOM）。
      - 小李根据系统推荐的库位（如遵循“先进先出”原则推荐最先入库的批次），走到对应的玻璃架前。
      - 扫描库位码，系统确认库位正确。
      - 扫描物料批次码，输入本次领用数量。
      - 点击“确认领用”。
  3.  **系统响应**:
      - 系统实时扣减库存。
      - 生产订单的物料耗用被准确记录，用于成本核算。
  4.  **期望**: 领料过程由系统指引，找料迅速；扫码操作杜绝了领错料、发错料的可能。

- **场景三：成品玻璃入库（铁架管理）**
  1.  **触发**: 生产线完成了一批成品玻璃的生产和包装。这批玻璃被统一放置在一个铁架上。
  2.  **操作路径**:
      - 小李为这个装满成品的铁架，生成并绑定一个新的、唯一的“容器条码”（铁架码）。
      - 在PDA上选择“生产入库”，扫描生产订单号。
      - 系统显示待入库的成品信息。
      - 小李扫描铁架码，并将该铁架上所有成品的批次信息与此铁架码关联。
      - 将铁架推到空闲库位，扫描库位码完成上架。
  3.  **系统响应**:
      - 系统库存中，不仅记录了这些成品的数量，还记录了它们存放在哪个铁架上，以及这个铁架在哪个库位。
  4.  **期望**: 实现对玻璃这种特殊产品的“带板/带架”管理，方便按整架进行追踪和调拨。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 基础设置 (F-WMS-01)
- **用户故事 (F-WMS-01-01)**: 作为一名**仓库主管**，我想要**对仓库、库区、货架、库位进行四级结构化管理**，以便**实现对库存物料的精确定位**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建和维护仓库、库区（如成品区、原料区）、货架、库位的基础信息。
      2. 每个库位有唯一的编码，并可生成对应的库位码标签。
      3. 库位可设置属性，如尺寸、承重、适用物料类型等。
    - **异常流程**:
      - 已有库存的库位，应限制删除。

### 3.2 入库管理 (F-WMS-02)
- **用户故事 (F-WMS-02-01)**: 作为一名**仓管员**，我想要**通过扫描采购单或生产单，执行收货和上架操作**，以便**提高入库效率和准确性**。
  - **详细描述**:
    - **正常流程**:
      1. 支持多种入库业务类型：采购入库、生产入库、销售退货入库、其他入库。
      2. 支持在收货时生成物料批次号和唯一条码。
      3. 支持按系统推荐或人工指定的库位进行扫码上架。
      4. 支持按“铁架”等容器为单位进行整体入库管理。
    - **异常流程**:
      - 实收数量与单据数量不符时（超收或少收），系统应提示并按预设策略处理（如允许差异或必须审批）。

### 3.3 出库管理 (F-WMS-03)
- **用户故事 (F-WMS-03-01)**: 作为一名**仓管员**，我想要**根据销售订单或生产工单，通过扫码进行拣货和发货**，以便**实现快速、零错误的出库作业**。
  - **详细描述**:
    - **正常流程**:
      1. 支持多种出库业务类型：销售发货、生产领料、采购退货出库、其他出库。
      2. 系统根据单据和库存策略（如先进先出），生成拣货任务单，并推荐最优拣货路径和库位。
      3. 作业人员通过扫描库位码和物料批次码进行拣货。
      4. 支持按“铁架”等容器为单位进行整体出库。
    - **异常流程**:
      - 拣货时扫描的物料或库位与任务单不符，系统应立刻报警提示。
      - 库存不足时，应禁止创建出库任务并提示。

### 3.4 库内管理 (F-WMS-04)
- **用户故事 (F-WMS-04-01)**: 作为一名**仓库主管**，我想要**发起和执行库存盘点任务**，以便**定期核对账面库存与实物库存的差异**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建盘点任务，可按库区、货架、物料等范围创建。
      2. 盘点时，作业人员扫描库位，清点实物数量，并在PDA中录入。
      3. 盘点结束后，系统自动生成盘点报告，清晰列出盘盈、盘亏的物料和差异数量。
      4. 支持对盘点差异进行调整，生成出入库调整单，使账实相符。
    - **异常流程**:
      - 盘点期间，被盘点的库位或物料应被冻结，禁止出入库操作。

- **用户故事 (F-WMS-04-02)**: 作为一名**仓管员**，我想要**执行库存的调拨和移位**，以便**整理仓库和优化存储**。
  - **详细描述**:
    - **正常流程**:
      1. 支持库存调拨（在不同仓库间转移）和库存移位（在同一仓库内不同库位间转移）。
      2. 所有操作均通过扫描物料/容器码和目标库位码完成，系统自动更新库存位置信息。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: PDA端操作响应时间应小于1秒。库存查询在百万级库存记录下，应在3秒内返回结果。
- **可靠性**: 所有出入库扫码操作记录不允许丢失，即使在网络不稳定的情况下，也应支持离线操作、在线同步的模式。
- **易用性**: PDA界面必须简洁、清晰，字体和按钮要足够大，方便在移动和光线不佳的场景下操作。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **库位主数据 (Location_Master)**
    - `location_id` (PK)
    - `location_code` (Unique)
    - `warehouse_id`
    - `zone_id`
    - `shelf_id`
    - ...

2.  **库存批次 (Inventory_Lot)**
    - `lot_id` (PK)
    - `item_id` (FK)
    - `lot_number` (Unique Batch No.)
    - `quantity`
    - `location_id` (FK)
    - `container_id` (FK, e.g., 铁架ID)
    - `entry_date`
    - ...

3.  **容器 (Container)**
    - `container_id` (PK)
    - `container_code` (Unique, e.g., 铁架码)
    - `container_type` (铁架, 托盘)
    - `location_id` (FK, 当前所在库位)
    - ...

4.  **库存交易流水 (Inventory_Transaction)**
    - `transaction_id` (PK)
    - `item_id` (FK)
    - `lot_id` (FK)
    - `transaction_type` (PO_IN, SO_OUT, PROD_IN, PROD_OUT)
    - `quantity_change` (+/-)
    - `source_document_id`
    - `timestamp`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### 扫码入库与出库流程
```mermaid
graph TD
    subgraph 入库流程
        A[采购订单到货] --> B[仓管员: PDA扫描采购单];
        B --> C[核对物料, 输入实收数];
        C --> D[系统/人工: 生成批次码];
        D --> E[仓管员: 扫码上架(扫描批次码和库位码)];
        E --> F[系统: 库存实时增加];
    end
    subgraph 出库流程
        G[生产订单需领料] --> H[仓管员: PDA扫描生产工单];
        H --> I{系统根据FIFO等策略<br/>推荐拣货库位和批次};
        I --> J[仓管员: 前往指定库位];
        J --> K[扫码拣货(扫描库位码和批次码)];
        K --> L[系统: 库存实时扣减];
    end
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-WMS-01 (精确入库)**:
  - **Given** 我收到一张采购订单的货物，共10件
  - **When** 我通过PDA扫描采购单，为货物生成批次码，并扫描A-01-01库位码完成上架
  - **Then** 系统库存查询中，必须能准确显示该物料的库存增加了10件，且这10件的存放位置是A-01-01。

- **AC-WMS-02 (无错出库)**:
  - **Given** 拣货任务单要求我去A-01-01库位，拣取批次号为LOT001的物料
  - **When** 我错误地扫描了B-02-03库位
  - **Then** PDA必须立刻发出声音或振动报警，并提示“库位错误”。

- **AC-WMS-03 (库存可追溯)**:
  - **Given** 我在库存查询界面，输入一个成品的批次号
  - **When** 我点击查询
  - **Then** 系统必须能反向追溯到该成品是由哪个生产订单生产的，以及它使用了哪些批次的原材料。
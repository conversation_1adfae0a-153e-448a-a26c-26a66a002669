# PRD-00: 系统核心业务流程整合概览

> **版本**: 1.0
> **状态**: 概览
> **撰写人**: <PERSON><PERSON> (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 引言

本文档旨在提供一个高阶视图，用于审阅“玻璃制品制造”业务线的核心流程设计。它将我们已分别定义的各个子系统串联起来，重点展示它们之间的数据流转和业务协同关系，以确保整体设计的无缝、高效和逻辑自洽。

---

## 2. 全系统PRD清单

以下是构成企业资源计划（ERP）系统的全部产品需求文档（PRD），您可以点击链接进行详细审阅：

*   **业务总览**: [`PRD-00: 系统核心业务流程整合概览`](./prd-00-integration-overview.md)
*   **核心基础**:
    *   [`PRD-01: 基础管理子系统`](./prd-01-basic-management-system.md)
    *   [`PRD-02: 工艺管理子系统 (PDM)`](./prd-02-pdm-system.md)
*   **核心业务线 - 定制生产**:
    *   [`PRD-03: 销售管理子系统`](./prd-03-sales-system.md)
    *   [`PRD-04: 采购管理子系统`](./prd-04-procurement-system.md)
    *   [`PRD-05: 生产管理子系统 (MES)`](./prd-05-production-system.md)
    *   [`PRD-06: 仓储管理子系统 (WMS)`](./prd-06-wms-system.md)
*   **核心业务线 - 项目制**:
    *   [`PRD-08: 项目管理子系统`](./prd-08-project-system.md)
*   **支持与协同**:
    *   [`PRD-07: 财务管理子系统`](./prd-07-finance-system.md)
    *   [`PRD-09: 质量管理子系统`](./prd-09-quality-system.md)
    *   [`PRD-10: 客户关系管理子系统 (CRM)`](./prd-10-crm-system.md)
    *   [`PRD-11: 人事管理子系统 (HR)`](./prd-11-hr-system.md)
*   **决策支持**:
    *   [`PRD-13: 数据中心 (BI)`](./prd-13-data-center.md)

---

## 3. 端到端核心业务流程图

此流程图展示了从一个客户意向开始，到最终产品交付入库的完整生命周期，以及各个子系统在其中扮演的角色。

```mermaid
graph TD
    %% --- 定义样式 ---
    classDef PDM fill:#f9f,stroke:#333,stroke-width:2px;
    classDef Sales fill:#ccf,stroke:#333,stroke-width:2px;
    classDef Purchase fill:#cfc,stroke:#333,stroke-width:2px;
    classDef MES fill:#ff9,stroke:#333,stroke-width:2px;
    classDef WMS fill:#9cf,stroke:#333,stroke-width:2px;
    classDef Quality fill:#f69,stroke:#333,stroke-width:2px;
    classDef Finance fill:#9f9,stroke:#333,stroke-width:2px;
    classDef HR fill:#f96,stroke:#333,stroke-width:2px;
    classDef CRM fill:#cff,stroke:#333,stroke-width:2px;

    %% --- 流程节点 ---
    subgraph "CRM"
        A(潜在客户/商机) --> B[报价/客户跟进];
    end

    subgraph "销售 (Sales)"
        B --> C[创建销售订单] --> D{订单确认};
    end

    subgraph "工艺 (PDM)"
        D --> E[生成/确认生产BOM];
    end

    subgraph "生产 (MES)"
        F[下达生产订单] --> G[工序执行];
        G --> H[完工报工];
    end
    
    subgraph "采购 (Purchase)"
        I[MRP运算] --> J[下达采购订单];
    end

    subgraph "仓储 (WMS)"
        K[采购入库] --> L{库存};
        L --> M[生产领料];
        N[完工入库] --> L;
        L --> O[销售发货];
    end

    subgraph "质量 (Quality)"
        Q1[IQC<br>来料检验]
        Q2[IPQC<br>过程检验]
        Q3[FQC<br>完工检验]
    end

    subgraph "财务 (Finance)"
        F1[应收<br>AR]
        F2[应付<br>AP]
        F3[成本核算]
    end

    subgraph "人事 (HR)"
        H1[计件<br>薪酬计算]
    end

    %% --- 连线与数据流 ---
    E --> I;
    E --> F;
    J --> K;
    F -- 领料 --> M;
    H --> N;
    D -- 发货 --> O;
    
    %% --- 协同流程连线 ---
    K -- 检验 --> Q1;
    G -- 检验 --> Q2;
    N -- 检验 --> Q3;
    
    O -- 应收 --> F1;
    K -- 应付 --> F2;
    H -- 成本 --> F3;
    H -- 计件数据 --> H1;
    H1 -- 人工成本 --> F3;

    %% --- 模块关联 ---
    class E PDM;
    class C,D Sales;
    class I,J Purchase;
    class F,G,H MES;
    class K,L,M,N,O WMS;
    class Q1,Q2,Q3 Quality;
    class F1,F2,F3 Finance;
    class H1 HR;
    class A,B CRM;
```

---

## 4. 关键数据流转说明（增强版）

1.  **从商机到订单 (CRM -> Sales -> PDM)**
    - **流程**: 【CRM】中的商机和报价，在客户确认后，转化为【销售管理】的正式销售订单。订单确认后，触发【工艺管理】生成与该订单关联的生产BOM。

2.  **从计划到执行 (PDM -> MES/Purchase -> WMS)**
    - **流程**: 固化的生产BOM成为核心输入。一方面，它触发【生产管理】下达生产订单；另一方面，它驱动MRP运算，生成【采购管理】的采购订单。生产和采购活动都与【仓储管理】进行紧密的出入库交互。

3.  **质量的全面渗透 (Quality)**
    - **IQC**: 【仓储】在执行采购入库时，通知【质量】进行来料检验。
    - **IPQC**: 【生产】在执行关键工序时，通知【质量】进行过程检验。
    - **FQC**: 【生产】的完工产品在入库前，通知【质量】进行最终检验。

4.  **业财一体化 (-> Finance)**
    - **应收**: 【仓储】的销售发货，触发【财务】生成应收账款。
    - **应付**: 【仓储】的采购入库（经IQC检验合格后），触发【财务】生成应付账款。
    - **成本**: 【生产】的完工报工数据，一方面将物料消耗计入成本；另一方面，将工时/计件数据传给【人事】。

5.  **人工成本的核算 (MES -> HR -> Finance)**
    - **流程**: 【生产】的完工报工数据，将工人的计件产量传递给【人事】系统。【人事】系统据此计算计件薪酬，并将计算出的“直接人工成本”传递给【财务】系统，用于最终的产品成本核算。

---

## 5. 下一步建议

请您和您的团队沿着上述流程图，结合各个子系统的详细PRD，进行一次全面的“桌面推演”。想象一个真实的订单，从头到尾走一遍，看看是否存在流程不畅、信息断点或功能缺失的地方。

在您完成审阅后，我们可以讨论并确定下一阶段的工作重点。

<ask_followup_question>
<question>这份整合概览是否清晰地展示了第一阶段设计的核心流程？您在审阅过程中，是否有发现任何潜在的流程冲突或需要澄清的地方？</question>
<follow_up>
<suggest>
流程图非常清晰，完美地将各子系统串联了起来。我没有问题了。
</suggest>
<suggest>
关于“BOM固化”后的变更流程，我们似乎没有定义。
</suggest>
<suggest>
关于“销售退货”的流程，在图上没有体现，我想了解一下。
</suggest>
<suggest>
我想增加一个“质量管理”的角色，看看它在流程中如何发挥作用。
</suggest>
</follow_up>
</ask_followup_question>
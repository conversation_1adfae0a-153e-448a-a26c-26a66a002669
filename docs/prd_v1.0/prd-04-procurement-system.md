# PRD-04: 采购管理子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: <PERSON>oo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
在以订单驱动生产的模式下，采购管理的及时性和准确性直接决定了生产能否按时启动、订单能否按期交付。传统手动的采购模式，由计划员根据销售订单和经验估算采购需求，不仅计算量大、容易出错（多算导致库存积压，少算导致停工待料），而且无法有效管理供应商和控制采购成本。本子系统旨在建立一个由数据驱动的、自动化的采购管理平台，实现采购业务全流程的精细化管控。

### 1.2 商业价值
- **降低库存成本**: 通过MRP（物料需求计划）精确计算物料需求，按需采购，减少不必要的库存资金占用。
- **避免生产延误**: 确保生产所需的物料能够按时、按量到位，避免因缺料导致的生产中断和交付延迟。
- **提升采购效率**: 自动化采购需求生成、采购订单创建等流程，将采购人员从繁琐的计算和文书工作中解放出来。
- **优化供应链协同**: 建立完善的供应商管理体系，实现与供应商的高效协同，并为采购寻源和价格谈判提供数据支持。

### 1.3 关键目标
- **目标1**: 建立统一的供应商档案管理，对供应商资质、价格、交付周期等进行全面管理。
- **目标2**: 实现MRP核心运算逻辑，能根据销售订单、BOM和库存数据，自动生成净采购需求。
- **目标3**: 规范采购订单的全生命周期管理，从创建、审批到收货、付款。
- **目标4**: 有效管理“部件外购”和“工序外协”两种不同的外协业务。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 刘姐
- **角色**: **采购经理 / 采购员**
- **背景**: 负责公司所有原材料、辅料及外协件的采购工作。每天需要处理来自生产部门的各种采购申请，与数十家供应商打交道。
- **核心诉求**:
    - "我不想再每天对着BOM表和库存表拿计算器算要买多少东西了，我希望系统能直接告诉我该买什么、买多少。"
    - "当一个供应商的交期或价格有变动时，我希望能方便地更新，并且在下采购单时能看到最新的信息。"
    - "我需要清晰地知道哪些是直接买回来的东西，哪些是发出去加工再回来的东西，它们的流程不一样。"

### 2.2 核心场景
- **场景一：MRP运算生成采购计划**
  1.  **触发**: 销售部门确认了一批新的销售订单。
  2.  **操作路径**:
      - 采购/计划员刘姐登录系统，进入【采购管理】->【MRP运算】。
      - 选择需要运算的销售订单范围（如“本周新增的所有订单”）。
      - 点击“开始运算”。
  3.  **系统响应**:
      - 系统自动获取这些销售订单关联的“生产BOM”。
      - 对BOM中的每一个物料，系统进行计算：`毛需求 = 订单需求量`。
      - 然后检查当前库存：`净需求 = 毛需求 - 可用库存`。
      - 系统将所有净需求 > 0 的物料汇总，生成一张“采购建议”清单。
      - 清单中列出：物料编码、名称、建议采购数量、期望到货日期（根据订单交期和采购提前期反推）。
  4.  **期望**: MRP运算快速准确，采购建议一目了然，采购员只需在此基础上进行确认和调整。

- **场景二：下达标准采购订单**
  1.  **触发**: 采购员刘姐需要根据“采购建议”购买玻璃原片。
  2.  **操作路径**:
      - 刘姐在“采购建议”列表中，勾选需要采购的玻璃原片。
      - 点击“转采购订单”。
      - 系统根据物料主数据中维护的“默认供应商”，自动按供应商对采购建议进行分组，并创建采购订单草稿。
      - 刘姐打开草稿，确认供应商、采购价格、数量、交期等信息。
      - 提交采购订单，进入审批流程。审批通过后，可将订单打印或通过邮件发送给供应商。
  3.  **期望**: 从采购建议到采购订单的转化流畅，大部分信息能自动带入，减少手动录入。

- **场景三：处理工序外协**
  1.  **触发**: 生产订单中有一道“特殊钢化”工艺需要外协处理。
  2.  **操作路径**:
      - 在生产订单的工序列表中，生产主管将该工序标记为“发起外协”。
      - 系统自动在采购模块生成一张“工序外协采购申请”。
      - 采购员刘姐接到申请，将其转为“工序外协采购订单”，并发送给外协厂。
      - 仓库根据外协订单，将需要加工的半成品玻璃“发货”给外协厂。
      - 外协厂加工完成后，将成品送回。仓库根据外协订单进行“收货”，并送去质检。
  3.  **期望**: 工序外协的物权不转移，系统能清晰地追踪发出物料和返回成品的全过程。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 供应商管理 (F-PC-01)
- **用户故事 (F-PC-01-01)**: 作为一名**采购经理**，我想要**维护一个完整的供应商信息库**，以便**进行有效的供应商关系管理和评估**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、查询、禁用供应商档案。
      2. 供应商信息应包含：基本信息（名称、地址、联系人）、财务信息（银行账户、税号）、资质文件（营业执照等附件）、供应范围、历史交易记录等。
      3. 支持对供应商进行评级和分类（如A类、B类供应商）。
    - **异常流程**:
      - 关键信息（如名称、统一社会信用代码）需保证唯一性。

### 3.2 物料需求计划 (MRP) (F-PC-02)
- **用户故事 (F-PC-02-01)**: 作为一名**计划员**，我想要**运行MRP来自动计算物料的净需求**，以便**准确地知道需要采购什么、何时采购**。
  - **详细描述**:
    - **正常流程**:
      1. 提供MRP运算界面，支持按订单、按时间、按物料等维度进行运算。
      2. 运算逻辑：`净需求 = (销售订单需求 + 安全库存) - (现有库存 + 在途采购)`。
      3. 运算结果以“采购建议”的形式呈现，包含物料、需求数量、建议供应商、期望到货日期等。
      4. 计划员可以对采购建议进行修改、合并或拆分。
    - **异常流程**:
      - 如果物料没有维护采购提前期或供应商，系统应在建议中明确提示。

### 3.3 采购订单管理 (F-PC-03)
- **用户故事 (F-PC-03-01)**: 作为一名**采购员**，我想要**方便地创建和管理采购订单**，以便**高效地完成采购任务并追踪订单状态**。
  - **详细描述**:
    - **正常流程**:
      1. 支持从“采购建议”自动生成，也支持手动创建。
      2. 采购订单有清晰的状态：草稿、待审批、已批准、已发货、部分收货、全部收货、已完成。
      3. **支持可配置的审批流**:
         - 应提供一个独立的审批流配置界面（或由基础平台提供统一能力）。
         - **支持按金额分级审批**: 管理员可以设置不同的金额区间，并为每个区间指定不同的审批路径（审批人/角色）。例如：`[0, 10000]` -> 采购经理审批; `(10000, 50000]` -> 部门总监审批。
         - 提交采购订单时，系统根据订单总金额自动匹配对应的审批流程。
      4. 支持采购订单的打印和导出。
    - **异常流程**:
      - 采购价格若高于系统中的参考价，应在审批时高亮提醒。
      - 若找不到匹配的审批流程，应禁止提交并提示管理员进行配置。

### 3.4 外协管理 (F-PC-04)
- **用户故事 (F-PC-04-01)**: 作为一名**采购员**，我想要**区分并管理部件外购和工序外协两种业务**，以便**准确地处理不同类型的采购流程**。
  - **详细描述**:
    - **正常流程**:
      1. **部件外购**: 流程同标准采购订单，采购的是一个完整的物料，入库后增加库存。
      2. **工序外协**:
         - 由生产任务触发，生成“外协订单”。
         - 外协订单关联需要发出的半成品和需要收回的成品。
         - 有专门的“外协发货”和“外协收货”操作，不直接增减库存，而是管理“委外在途”物资。
    - **异常流程**:
      - 外协收货时，若数量与发货数量不符，系统应提示并要求处理（如报损）。

---

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: MRP运算，在100个销售订单（平均每个BOM有10行）的规模下，运算时间应小于2分钟。
- **安全性**: 采购价格、供应商信息为敏感数据，需严格控制访问权限。
- **可用性**: 核心功能可用性不低于99.9%。
- **集成性**: 需与销售、库存、生产、财务等模块紧密集成。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **供应商 (Supplier)**
    - `supplier_id` (PK)
    - `supplier_code` (Unique)
    - `supplier_name`
    - `contact_info` (JSON)
    - `address`
    - ...

2.  **采购订单头 (PO_Header)**
    - `po_id` (PK)
    - `po_number` (Unique)
    - `supplier_id` (FK)
    - `po_type` (标准采购, 工序外协)
    - `status` (待审批, 已批准, ...)
    - `total_amount`
    - ...

3.  **采购订单行 (PO_Line)**
    - `po_line_id` (PK)
    - `po_id` (FK)
    - `item_id` (FK, 采购的物料)
    - `quantity`
    - `unit_price`
    - `expected_delivery_date`
    - `received_quantity`
    - ...

4.  **外协订单特定信息 (Subcontract_Info)**
    - `po_line_id` (FK)
    - `source_production_order_id` (FK)
    - `item_to_send_id` (FK, 发出的半成品)
    - `quantity_to_send`
    - ...

---

## 6. 核心业务流程图 (Core Business Flowchart)

### MRP驱动的采购流程
```mermaid
graph TD
    A[销售订单确认] --> B{运行MRP};
    B --> C[计算毛需求];
    C --> D[考虑现有库存和在途];
    D --> E[生成净需求采购建议];
    E --> F[采购员审核/调整建议];
    F --> G{按供应商合并};
    G --> H[生成采购订单];
    H --> I[审批流程];
    I -- 通过 --> J[发送给供应商];
    J --> K[收货入库];
    K --> L[质检];
    L --> M[财务付款];
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-PC-01 (MRP运算准确性)**:
  - **Given** 销售订单需要A产品10个，A的BOM由1个B和2个C构成。当前库存B有2个，C有5个。
  - **When** 我运行MRP
  - **Then** 系统生成的采购建议必须是：采购B共8个 (10*1-2)，采购C共15个 (10*2-5)。

- **AC-PC-02 (采购订单生成)**:
  - **Given** 我在采购建议列表中勾选了需要向同一供应商采购的3种物料
  - **When** 我点击“转采购订单”
  - **Then** 系统必须能自动生成一张包含这3行物料明细的、发往该供应商的采购订单草稿。

- **AC-PC-03 (外协流程)**:
  - **Given** 我有一张“工序外协”类型的采购订单，需要将10片半成品玻璃发给供应商进行钢化
  - **When** 仓库执行“外协发货”操作，数量为10
  - **Then** 系统中该半成品玻璃的库存不应减少，但“委外在途”数量应增加10。
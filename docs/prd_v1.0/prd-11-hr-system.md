# PRD-11: 人事管理子系统 (HR Management Subsystem)

## 1. 背景 (Background)

本公司拥有定制化的玻璃制造产线和项目制的工程业务。其中，生产线的薪酬体系具有显著的行业特色，大量一线操作人员的工资与其实际完成的工序和产量直接挂钩，即“计件薪酬”。为了精确核算人工成本、激励员工、并实现业财一体化，需要一个强大的人事管理子系统。

该系统不仅要覆盖标准的人事信息管理、考勤等功能，其核心任务是与生产执行系统（MES）和财务系统无缝对接，实现计件薪酬的自动、准确计算与核算，并将人工成本正确归集到生产订单和产品成本中。

## 2. 用户画像 (Personas)

| 画像 (Persona) | 角色 (Role) | 核心需求 (Key Needs) |
| :--- | :--- | :--- |
| 李师傅 | 生产线操作工 (Production Line Worker) | - 清晰查看自己的计件产量和每日薪酬。<br>- 确认工时和考勤记录。<br>- 对薪酬明细有疑问时可以追溯。 |
| 王主管 | 生产车间主管 (Workshop Supervisor) | - 实时掌握班组的生产效率和计件统计。<br>- 审核确认本班组工人的工时与产量数据。<br>- 轻松处理异常工时（如设备维修、待料）的工时记录。 |
| 张女士 | HR专员 (HR Specialist) | - 管理员工档案、入离职、合同等。<br>- 设置和维护计件单价（按工序）。<br>- 自动生成全公司（特别是生产部门）的工资条。<br>- 准确、高效地完成算薪和发薪流程。 |
| 陈会计 | 成本会计 (Cost Accountant) | - 获取由HR系统计算的、准确到每个生产订单的人工成本。<br>- 将直接人工成本和间接人工成本进行归集与分摊。<br>- 确保人事数据与财务凭证的自动同步。 |

## 3. 功能需求 (Functional Requirements)

### 3.1 组织与员工档案管理
- **FR3.1.1 组织架构管理**: 支持公司、部门、班组等多层级组织架构的维护。
- **FR3.1.2 员工档案**: 电子化管理员工基本信息、合同信息、岗位信息、薪酬信息、入离职记录等。
- **FR3.1.3 员工自助门户**: 员工可通过手机端或PC端查询个人信息、考勤记录、工资条等。

### 3.2 考勤管理
- **FR3.2.1 班次与排班**: 支持固定班次、倒班等多种排班规则。
- **FR3.2.2 考勤数据采集**: 对接考勤机，自动获取打卡记录。支持移动端打卡。
- **FR3.2.3 异常考勤处理**: 员工可在线提交请假、加班、出差等申请，由主管审批。
- **FR3.2.4 考勤报表**: 自动生成月度考勤汇总报表，作为薪酬计算的依据之一。

### 3.3 薪酬管理 (核心)
- **FR3.3.1 薪酬结构定义**: 支持定义多种薪酬结构，包括基本工资、岗位工资、绩效、津贴、以及计件工资。
- **FR3.3.2 计件单价维护 (与PDM/MES集成)**:
    - HR部门可以为`工艺管理(PDM)`中定义的每一道标准“工序”设定计件单价。
    - 支持按不同产品、不同工序难度设置不同的单价。
    - 单价变更需有记录和审批流程。
- **FR3.3.3 计件数据自动采集 (与MES集成)**:
    - 系统自动从`生产管理(MES)`子系统获取经过车间主管确认的“工序汇报”数据。
    - 数据包含：操作工、生产订单号、产品、工序号、完成合格数量、完成日期等。
- **FR3.3.4 薪酬计算引擎**:
    - 月末，系统根据考勤数据计算员工的基本工资和各项固定津贴。
    - 根据从MES获取的计件数据，自动计算每位工人的计件工资 `(计件工资 = ∑ (工序完成合格数 * 对应工序单价))`。
    - 汇总生成每位员工的应发工资、扣款、实发工资，并生成详细工资条。
- **FR3.3.5 薪酬审核与发放**: HR确认计算结果后，可提交至财务审批，并标记发放状态。

### 3.4 成本归集 (与财务系统集成)
- **FR3.4.1 直接人工成本计算**: 系统将每个工人的计件工资，按其关联的生产订单，自动归集为直接人工成本。
- **FR3.4.2 间接人工成本分摊**: 对于车间管理人员、后勤人员等非计件岗位的工资，可按设定的规则（如按工时、按产线）分摊计入制造费用。
- **FR3.A.3 凭证生成**: 经确认的工资数据和成本归集数据，自动在`财务管理`子系统中生成记账凭证。

## 4. 非功能需求 (Non-Functional Requirements)

- **NF4.1 性能**: 算薪模块需在月底高峰期2小时内完成500名生产员工的薪酬计算。
- **NF4.2 准确性**: 薪酬计算，特别是计件工资部分，准确率必须达到100%。任何计算逻辑的变更都需要经过严格测试。
- **NF4.3 集成性**: 必须与PDM、MES、财务系统实现稳定、实时的数据接口。
- **NF4.4 安全性**: 薪酬和员工个人信息属于高度敏感数据，必须有严格的权限控制，只有授权角色（如HR专员、财务经理）可见。所有对薪酬数据的关键操作必须留痕。
- **NF4.5 易用性**: 员工自助查询界面应简洁明了，易于操作。HR和主管的操作界面应高效，减少手动数据录入。

## 5. 数据模型 (Data Model)

- **Employee**: (EmployeeID, Name, DepartmentID, Position, HireDate, Status, BasicSalary)
- **AttendanceRecord**: (RecordID, EmployeeID, Date, CheckInTime, CheckOutTime, WorkHours, LeaveType)
- **PieceRatePrice**: (PriceID, ProcessID, Price, EffectiveDate, ExpiryDate) `// 关联PDM的工序表`
- **WorkReport**: (ReportID, EmployeeID, ProductionOrderID, ProcessID, QualifiedQty, ReportDate) `// 从MES同步`
- **Payslip**: (PayslipID, EmployeeID, Period, BasicSalary, PieceworkSalary, Allowances, Deductions, NetSalary, Status)
- **LaborCostAllocation**: (AllocationID, PayslipID, ProductionOrderID, AllocatedAmount, CostType) `// 成本归集结果`

## 6. 核心流程图 (Flowchart)

### 计件薪酬计算与成本归集流程

```mermaid
graph TD
    subgraph PDM/MES
        A[工艺工程师定义标准工序] --> B(HR专员维护工序计件单价)
        C[工人在产线完成工序<br>并通过终端汇报产量] --> D{车间主管审核<br>确认产量数据}
    end

    subgraph HR
        E[考勤模块提供<br>月度出勤数据]
        D --> F[系统自动拉取<br>已确认的计件产量]
        B & F & E --> G[薪酬计算引擎]
        G --> H{生成工资条草稿}
        H --> I[HR专员审核确认]
    end

    subgraph Finance
        I --> J[工资数据进入<br>财务审批流程]
        J --> K[系统按生产订单<br>归集直接人工成本]
        K --> L[自动生成<br>财务记账凭证]
    end

    subgraph Employee
       I --> M[员工可在自助门户<br>查询工资条明细]
    end
```

## 7. 验收标准 (Acceptance Criteria)

- **AC7.1**: HR专员可以成功为 PDM 中“玻璃切割”工序设置计件单价为 0.5元/件。
- **AC7.2**: 生产工“李师傅”在MES系统中汇报完成了“S001”订单的“玻璃切割”工序
100件，该记录被主管确认后，系统能正确同步此条计件工作记录。
- **AC7.3**: 月底进行薪酬计算时，系统能为“李师傅”自动计算出至少 50元 (100件 * 0.5元/件) 的计件工资，并体现在他的工资条草稿中。
- **AC7.4**: 财务人员在系统中可以查看到，“S001”订单的人工成本增加了 50元。
- **AC7.5**: 当月工资总账凭证中，”应付职工薪酬“科目下能正确体现包含李师傅在内的所有员工的薪资总额。
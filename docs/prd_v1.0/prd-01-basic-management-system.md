# PRD-01: 基础管理子系统 产品需求文档

> **版本**: 1.0
> **状态**: 草稿
> **撰写人**: <PERSON>oo (资深产品经理)
> **日期**: 2025-07-29

---

## 1. 背景与目标 (Background & Goals)

### 1.1 背景
本系统旨在为玻璃深加工及相关企业提供一套全面的信息化解决方案。基础管理子系统是整个企业资源规划（ERP）系统的核心基础，它为所有其他业务子系统（如销售、采购、生产、财务等）提供统一的、标准化的组织架构、用户权限、及基础数据管理能力。没有一个稳定、灵活、安全的基础平台，上层业务系统将无法有效运转，数据一致性与业务流程的规范性也无从谈起。

### 1.2 商业价值
- **统一管理标准**: 建立全公司统一的用户、角色、部门和数据标准，消除信息孤岛。
- **保障数据安全**: 通过精细化的权限控制，确保不同岗位的员工只能访问其职责范围内的数据，保护企业核心信息资产。
- **提升运营效率**: 为各业务流程提供稳定、一致的基础数据，减少因数据不一致导致的沟通成本和操作失误。
- **支持集团化发展**: 通过灵活的多组织架构设计，支撑企业未来多工厂、多事业部、多法人实体的集团化管控模式。

### 1.3 关键目标
- **目标1**: 建立一个支持多组织架构的企业模型，能够对部门、岗位、员工进行有效管理。
- **目标2**: 实现一个灵活的、基于角色（RBAC）的权限控制体系，能够对功能权限和数据权限进行精细化管理。
- **目标3**: 提供一个统一的数据字典管理中心，对系统中所有关键业务对象的基础数据进行集中维护。
- **目标4**: 确保系统的基础配置（如系统参数、编码规则）可以被授权管理员灵活调整，以适应业务变化。

---

## 2. 用户画像与核心场景 (User Personas & Core Scenarios)

### 2.1 用户画像
- **姓名**: 张伟
- **角色**: **系统管理员**
- **背景**: 公司IT部门负责人，拥有最高的系统管理权限。负责系统的初始化配置、用户账号创建、权限分配、组织架构维护等工作。
- **核心诉求**:
    - "我需要一个强大而直观的管理后台，能让我快速完成新员工的入职配置。"
    - "当业务部门调整时，我希望能方便地修改组织架构，并批量调整相关人员的权限。"
    - "我必须确保系统的权限是严格受控的，防止数据泄露。"

### 2.2 核心场景
- **场景一：新员工入职**
  1.  **触发**: 人事部通知IT部，销售部新入职一名“销售代表”李明。
  2.  **操作路径**:
      - 系统管理员张伟登录系统后台。
      - 进入【组织架构】->【用户管理】模块。
      - 点击“新增用户”，填写李明的基本信息（姓名、工号、联系方式），并设置初始登录密码。
      - 将李明的账号状态设置为“启用”。
      - 进入【权限管理】->【角色分配】模块。
      - 找到用户“李明”，为其分配预设好的“销售代表”角色。
  3.  **系统响应**:
      - 系统自动将“销售代表”角色所拥有的全部功能权限（如查看产品目录、创建销售订单）和数据权限（如只能查看自己负责的客户）赋予李明。
      - 李明使用初始密码首次登录时，系统会强制其修改密码。
  4.  **期望**: 整个过程在5分钟内完成，操作简单，不易出错。

- **场景二：业务部门调整**
  1.  **触发**: 公司决定将“防火窗销售组”从“销售一部”调整至“项目部”。
  2.  **操作路径**:
      - 系统管理员张伟进入【组织架构】->【部门管理】模块。
      - 通过拖拽或编辑操作，将“防火窗销售组”这个部门节点，从“销售一部”下移动到“项目部”下。
  3.  **系统响应**:
      - 系统自动更新所有原属于“防火窗销售组”员工的部门归属。
      - 如果数据权限是按部门层级设置的，这些员工的数据可见范围将自动根据新的部门归属进行调整。
  4.  **期望**: 部门调整能即时生效，相关员工的权限自动更新，无需逐一手动修改。

---

## 3. 功能需求列表 (Functional Requirements List)

### 3.1 组织架构管理 (F-BM-01)
- **用户故事 (F-BM-01-01)**: 作为一名**系统管理员**，我想要**维护公司的多级组织架构**，以便**反映真实的汇报关系和业务单元划分**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、删除、查询组织（公司、事业部、部门、小组等）。
      2. 组织架构应以树状结构展示，层级不限，支持拖拽调整。
      3. 每个组织节点应包含关键属性：组织编码、组织名称、负责人、组织类型（如成本中心、利润中心）。
      4. 基于我们达成的共识，系统必须支持多组织架构，允许定义多个独立的法人公司或业务单元。
    - **异常流程**:
      - 删除组织时，若该组织下仍有员工，应提示管理员先转移员工。
      - 组织编码需保证全局唯一。

### 3.2 用户与岗位管理 (F-BM-02)
- **用户故事 (F-BM-02-01)**: 作为一名**系统管理员**，我想要**管理员工的账号信息和岗位信息**，以便**为他们分配系统访问权限**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、禁用/启用、删除用户账号。
      2. 用户信息应包含：工号、姓名、登录账号、密码、所属部门、岗位、联系方式、状态（启用/禁用）。
      3. 支持岗位管理，可定义不同岗位（如“销售经理”、“采购员”），用户可关联一个或多个岗位。
      4. 支持重置用户密码和批量导入用户。
    - **异常流程**:
      - 登录账号和工号需保证全局唯一。
      - 禁用用户后，该用户应无法登录系统。

### 3.3 权限管理 (RBAC) (F-BM-03)
- **用户故事 (F-BM-03-01)**: 作为一名**系统管理员**，我想要**创建和管理角色，并为角色配置权限**，以便**实现对不同岗位人员的权限标准化管理**。
  - **详细描述**:
    - **正常流程**:
      1. 支持创建、编辑、删除、查询角色（如“销售代表”、“生产计划员”）。
      2. 支持为角色批量分配“菜单权限”、“操作权限”（如增、删、改、查、审核、打印等）。
      3. 权限列表应与系统功能菜单树保持一致，方便勾选。
    - **异常流程**:
      - 删除角色时，需提示管理员该角色已关联的用户将失去相应权限。

- **用户故事 (F-BM-03-02)**: 作为一名**系统管理员**，我想要**将角色赋予用户**，以便**快速为用户授予或撤销一套权限**。
  - **详细描述**:
    - **正常流程**:
      1. 支持为一个用户分配一个或多个角色。
      2. 支持为一个角色批量关联多个用户。
      3. 用户的最终权限是其所有角色权限的合集。
    - **异常流程**:
      - 当用户的角色被移除后，其对应的权限应被实时收回。

- **用户故事 (F-BM-03-03)**: 作为一名**系统管理员**，我想要**配置数据权限规则**，以便**控制用户能看到的数据范围（如只能看本部门的订单）**。
  - **详细描述**:
    - **正常流程**:
      1. 支持按角色配置数据权限规则。
      2. 规则类型应至少支持：仅本人、本部门、本部门及下属部门、指定部门、全部数据。
      3. 此规则可应用于核心业务对象，如客户、订单、采购单等。
    - **异常流程**:
      - 未配置数据权限的角色，默认应采用最严格的“仅本人”规则。

### 3.4 数据字典管理 (F-BM-04)
- **用户故事 (F-BM-04-01)**: 作为一名**系统管理员**，我想要**维护系统中通用的基础数据**，以便**规范业务数据的录入标准**。
  - **详细描述**:
    - **正常流程**:
      1. 提供统一界面，管理各类基础数据，如：单位（米、片、个）、玻璃类型、颜色、厚度、工艺类型、客户来源、付款方式等。
      2. 支持对字典类型和字典值进行增删改查和排序。
      3. 业务表单中的下拉框选项应直接来源于数据字典。
    - **异常流程**:
      - 正在被业务数据引用的字典值，应限制删除，或提供数据迁移方案。

---

### 3.5 系统日志管理 (F-BM-05)
- **用户故事 (F-BM-05-01)**: 作为一名**系统管理员**，我想要**查询系统中的关键操作日志**，以便**进行安全审计和问题追溯**。
  - **详细描述**:
    - **正常流程**:
      1. 提供一个日志查询界面。
      2. 支持根据时间范围、操作用户、功能模块、IP地址等条件进行组合查询。
      3. 日志列表应清晰展示：操作时间、操作人、IP地址、操作类型（如新增、修改、删除）、操作对象及简要内容。
      4. 记录范围应覆盖所有关键操作，如：用户登录、权限变更、组织架构修改、关键业务单据的创建与删除等。
    - **异常流程**:
      - 查询条件无效时，应给出友好提示。
      - 日志数据应定期归档，以防数据库无限膨胀。

## 4. 非功能性需求 (Non-Functional Requirements)

- **性能**: 核心操作（如用户登录、权限校验）响应时间应小于500毫秒。组织架构树在1000个节点内加载时间应小于2秒。
- **安全性**:
    - 用户密码必须加密存储。
    - 所有API请求必须经过身份认证和权限校验。
    - 防止SQL注入、XSS等常见Web攻击。
    - 提供操作日志，记录关键操作（如用户创建、权限变更）的执行人、时间和IP地址。
- **可用性**: 系统需保证7x24小时可用，核心功能可用性不低于99.9%。
- **兼容性**: 后台管理界面应兼容主流浏览器（Chrome, Firefox, Edge）的最新版本。

---

## 5. 数据模型 / 关键实体 (Data Model / Key Entities)

1.  **组织 (Organization)**
    - `org_id` (PK)
    - `org_code` (Unique)
    - `org_name`
    - `parent_id` (FK to Organization)
    - `type` (法人公司, 事业部, 部门)
    - `manager_id` (FK to User)
    - ...

2.  **用户 (User)**
    - `user_id` (PK)
    - `user_name` (Login Name, Unique)
    - `password_hash`
    - `full_name`
    - `employee_id` (工号, Unique)
    - `org_id` (FK to Organization)
    - `status` (启用, 禁用)
    - ...

3.  **角色 (Role)**
    - `role_id` (PK)
    - `role_name` (Unique)
    - `description`
    - ...

4.  **权限 (Permission)**
    - `permission_id` (PK)
    - `permission_code` (e.g., 'order:create', 'user:delete')
    - `permission_name`
    - `type` (菜单, 操作)
    - ...

5.  **用户-角色关联 (User_Role_Map)**
    - `user_id` (FK)
    - `role_id` (FK)

6.  **角色-权限关联 (Role_Permission_Map)**
    - `role_id` (FK)
    - `permission_id` (FK)

7.  **数据字典类型 (Dict_Type)**
    - `type_id` (PK)
    - `type_code` (e.g., 'glass_type', Unique)
    - `type_name`

8.  **数据字典值 (Dict_Value)**
    - `value_id` (PK)
    - `type_id` (FK)
    - `value_label`
    - `value_data`
    - `sort_order`

9.  **操作日志 (Operation_Log)**
    - `log_id` (PK)
    - `user_id` (FK)
    - `user_name`
    - `ip_address`
    - `timestamp`
    - `action_type` (CREATE, UPDATE, DELETE, LOGIN)
    - `module` (e.g., 'UserManagement', 'Order')
    - `details` (JSON or Text, to store details of the operation)
    - ...

---

9.  **操作日志 (Operation_Log)**
    - `log_id` (PK)
    - `user_id` (FK)
    - `user_name`
    - `ip_address`
    - `timestamp`
    - `action_type` (CREATE, UPDATE, DELETE, LOGIN)
    - `module` (e.g., 'UserManagement', 'Order')
    - `details` (JSON or Text, to store details of the operation)
    - ...

## 6. 核心业务流程图 (Core Business Flowchart)

### 角色权限分配流程 (Mermaid)
```mermaid
graph TD
    A[系统管理员登录系统] --> B{进入权限管理模块};
    B --> C[1. 角色管理];
    C --> C1[创建/编辑角色, e.g., 销售代表];
    B --> D[2. 权限分配];
    D --> D1[为'销售代表'角色勾选权限];
    D1 --> D2[菜单权限: 订单管理, 客户管理];
    D1 --> D3[操作权限: 新增订单, 查看客户];
    B --> E[3. 用户分配];
    E --> E1[选择用户'李明'];
    E1 --> E2[为'李明'关联'销售代表'角色];
    E2 --> F[保存设置];
    F --> G[李明登录后, 即拥有相应权限];
```

---

## 7. 验收标准 (Acceptance Criteria)

- **AC-BM-01 (组织架构管理)**:
  - **Given** 我是系统管理员
  - **When** 我在组织架构管理页面创建一个新的部门，并将其拖拽到“销售部”之下
  - **Then** 系统中该部门的归属关系立刻更新，且在组织树上正确显示。

- **AC-BM-02 (用户与角色分配)**:
  - **Given** 我是系统管理员，并且已创建一个名为“采购员”的角色
  - **When** 我创建一个新用户“王五”，并将“采购员”角色分配给他
  - **Then** “王五”登录系统后，其可见的菜单和可执行的操作，必须与“采购员”角色被授予的权限完全一致。

- **AC-BM-03 (数据权限)**:
  - **Given** “销售代表”角色的数据权限被设置为“仅本人”
  - **And** 用户“李明”和“赵四”都是“销售代表”
  - **When** “李明”登录系统并进入订单列表页面
  - **Then** 他应该只能看到由他自己创建的订单，绝对不能看到“赵四”创建的订单。
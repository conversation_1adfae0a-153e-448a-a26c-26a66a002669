# PRD-05: 智能制造数字化工厂核心运营平台 (改进版)

> **版本**: 2.2 (质量优化版)  
> **状态**: 需求已确认  
> **撰写人**: 产品经理 (Roo)  
> **日期**: 2025-07-30  
> **行业聚焦**: 玻璃深加工及高附加值细分领域

---

## 术语表 (Glossary)

| 术语 | 英文全称 | 中文定义 | 说明 |
|------|----------|----------|------|
| **APS** | Advanced Planning and Scheduling | 高级计划与排程系统 | 用于优化生产计划和资源调度的智能系统 |
| **MES** | Manufacturing Execution System | 制造执行系统 | 连接计划层与执行层的生产管理系统 |
| **OEE** | Overall Equipment Effectiveness | 设备综合效率 | 衡量设备利用率的关键指标 |
| **UID** | Unique Identifier | 唯一标识符 | 为每个产品分配的全局唯一身份码 |
| **工序任务** | Work Order Task | 工序任务 | 生产计划分解后的最小执行单元 |
| **IIoT** | Industrial Internet of Things | 工业物联网 | 工业设备的网络化连接和数据交换 |

---

## 1. 项目概述

### 1.1 项目范围

#### 包含范围 (In-Scope)
1. **智能生产调度中心 (F-PD-1)**:
   - [x] 订单自动分解为工序任务
   - [x] 支持定义多目标、多约束的排程场景
   - [x] "一键式"智能排程功能的核心流程
   - [x] 以甘特图形式可视化呈现排程结果
   - [x] 对排程结果进行关键指标解读（计划摘要）
   - [x] 支持对计划进行有限的人工干预与模拟推演

2. **智能车间执行中心 (F-PD-2)**:
   - [x] 工位终端的任务队列展示与数字化SOP查阅
   - [x] 基于扫码的工序报工（含合格、不合格上报）
   - [x] 关键工序（钢化/夹胶）的过程数据自动采集（模拟）
   - [x] 面向车间主管的实时电子看板（进度、Andon、质量）

3. **全生命周期质量追溯 (F-PD-3)**:
   - [x] UID的生成与绑定流程
   - [x] 关键工序扫码关联操作信息
   - [x] 质量追溯门户的核心查询与"数字档案"展示功能

4. **设备物联与数据驱动决策 (F-PD-4)**:
   - [x] OEE核心指标的计算与可视化驾驶舱
   - [x] 停机原因的柏拉图分析

#### 暂不包含范围 (Out-of-Scope)
- **[!] 高级算法的深度配置**: 本次迭代不提供APS算法参数的图形化配置界面
- **[!] 预测性维护**: 预测性维护的复杂建模与预警功能，本次仅做数据采集
- **[!] 完整的系统集成对接**: 与ERP、WMS、PLM的接口将通过模拟数据进行
- **[!] 人员技能矩阵管理**: 排程约束中的人员技能约束，本次迭代简化处理
- **[!] 计件工资核算**: MES到ERP的工时上报接口本次不实现
- **[!] 复杂的权限管理界面**: 本次迭代仅实现核心角色的权限控制

### 1.2 愿景与战略目标

#### 新愿景：从"数字化车间"到"智能制造核心平台"
将MES从被动的生产执行系统，升级为主动的、具备感知、决策和优化能力的数字化工厂核心平台。它将成为驱动整个工厂运营的"智能大脑"，深度赋能玻璃深加工及其高附加值延伸领域的复杂制造场景。

#### 商业价值主张
- **驱动卓越运营**: 通过全局最优的智能调度，最大化资源利用率，最小化生产周期与成本
- **赋能价值创造**: 以全生命周期质量追溯能力，满足高端产品的强制性认证要求
- **数据驱动决策**: 将生产现场的数据转化为业务洞察，实现从"经验驱动"到"数据驱动"的转变
- **构建生态中枢**: 作为连接企业管理层、设计端和物理世界的核心枢纽，实现端到端的业务流程协同

#### 四大核心战略目标
- **目标1：构建智能调度与执行引擎**: 设计一套能处理高度定制化、小批量、多品种订单的智能排程引擎
- **目标2：建立全生命周期质量追溯体系**: 建立从原片到成品的精细化质量追溯链
- **目标3：深化设备物联与数据洞察**: 实现MES与核心设备的深度集成，完成生产指令的自动下发和工艺参数的自动采集
- **目标4：强化系统生态集成**: 明确定义MES作为工厂运营中枢的双向数据流和业务协同逻辑

---

## 2. 用户画像与核心场景

### 2.1 用户画像

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **陈工** - 生产计划与调度工程师 | 负责管理APS引擎，设定优化目标和约束条件，处理复杂异常 | 需要系统提供全局最优的生产计划，当设备故障时能快速重排 |
| **李师傅** - 智能化产线操作员 | 在设备旁的工业平板上工作，接收MES指令，监控设备状态 | 需要精准执行指令，系统自动报警并提示异常处理 |
| **王经理** - 质量与工艺工程师 | 负责建立和维护全生命周期质量追溯体系，利用追溯数据进行持续改进 | 需要快速调出产品从原片到成品的所有数据进行质量分析 |
| **赵总** - 工厂运营总监 | 关注工厂整体的运营效率和效益，通过BI驾驶舱做决策 | 需要实时看到工厂OEE，了解瓶颈环节和设备停机损失 |

### 2.2 核心场景

#### 场景一：APS引擎的"一键排程"与计划下发
**触发条件**: 一周内积累了50个销售订单，涵盖上千种不同规格的玻璃

**操作流程**:
1. 计划工程师陈工进入【智能生产调度中心】
2. 设定本周的核心优化目标：优先保证防火窗订单交期、最大化原片套裁利用率、尽可能合并钢化炉次
3. 点击"启动智能排程"

**系统响应**:
- APS引擎自动分解所有订单，读取设备产能日历、实时物料库存等约束
- 复杂的优化算法在几分钟内完成运算，生成满足所有约束且在目标上最优的生产计划
- 系统以甘特图形式呈现计划，并附上"计划分析报告"
- 陈工确认计划后，点击"下发计划"，所有工序任务被自动推送到对应工位

**期望结果**: 将计划员数小时的工作，缩短为几分钟的"设定目标+确认结果"

#### 场景二：基于"数字身份"的全生命周期质量追溯
**触发条件**: 客户要求提供某批次玻璃的完整生产过程数据

**操作流程**:
1. 质量工程师王经理扫描玻璃上的二维码，获取UID: `PN-20250729-LOT-A-135`
2. 进入【全生命周期质量追溯】系统，输入此UID

**系统响应**:
- 系统呈现可视化的"数字档案"报告
- 显示该玻璃的完整生命周期：源头原片批次、切割信息、钢化工艺曲线、组装过程、检验结果、交付信息

**期望结果**: 实现单片级别的、不可篡改的、贯穿全流程的精细化质量追溯

---

## 3. 功能需求

### 3.1 智能生产调度中心 (F-PD-1)

#### 需求 3.1.1: 订单自动分解
**需求描述**: 系统应能自动将已确认的销售订单分解为可执行的工序级任务池

**验收标准**:
- [ ] 能够在5分钟内完成50个订单的自动分解
- [ ] 分解成功率达到95%以上
- [ ] 异常订单能够清晰展示失败原因
- [ ] 支持手动触发和自动轮询两种分解方式

**优先级**: 高  
**依赖**: ERP系统订单数据、PLM系统工艺路线数据

#### 需求 3.1.2: 排程场景配置
**需求描述**: 系统应支持定义包含多重约束和多维目标的排程场景

**验收标准**:
- [ ] 支持创建和命名排程场景
- [ ] 提供任务筛选和圈选功能
- [ ] 支持拖拽调整优化目标优先级
- [ ] 能够检测和预警物料短缺冲突
- [ ] 支持设备约束的临时调整

**优先级**: 高  
**依赖**: 物料库存数据、设备产能日历

#### 需求 3.1.3: 智能排程引擎
**需求描述**: 系统应提供一键启动的智能排程功能，自动生成全局最优的生产计划

**验收标准**:
- [ ] 排程计算时间不超过10分钟（1000个任务）
- [ ] 生成的计划准时交付率达到90%以上
- [ ] 支持甘特图可视化展示
- [ ] 提供计划摘要和关键KPI分析
- [ ] 能够处理无解情况并提供冲突分析

**优先级**: 高  
**依赖**: APS算法引擎、切割优化服务

#### 需求 3.1.4: 计划干预与模拟
**需求描述**: 系统应支持对智能生成的计划进行人工干预和模拟推演

**验收标准**:
- [ ] 支持拖拽调整任务开始时间
- [ ] 实时计算和显示调整影响
- [ ] 支持多版本计划保存和对比
- [ ] 提供计划锁定和下发功能
- [ ] 能够检测和阻止硬约束冲突

**优先级**: 中  
**依赖**: 实时计算引擎

### 3.2 智能车间执行中心 (F-PD-2)

#### 需求 3.2.1: 工位任务管理
**需求描述**: 操作员应能在工位终端上清晰看到任务队列，并获取所有生产所需信息

**验收标准**:
- [ ] 任务列表按计划开始时间排序
- [ ] 支持查看电子图纸、SOP、工艺参数
- [ ] 开工前自动检查物料和设备状态
- [ ] 界面响应时间不超过2秒
- [ ] 支持多种登录方式（工号密码、刷卡）

**优先级**: 高  
**依赖**: WMS系统、设备状态数据

#### 需求 3.2.2: 自动化报工
**需求描述**: 操作员应能通过扫码或设备联动方式，自动或半自动完成数据上报

**验收标准**:
- [ ] 支持手动和设备自动报工
- [ ] 报工数据校验准确率达到99%
- [ ] 异常上报响应时间不超过30秒
- [ ] 支持不合格品原因分类统计
- [ ] 过程数据自动采集和绑定

**优先级**: 高  
**依赖**: 设备集成接口、扫码设备

#### 需求 3.2.3: 车间电子看板
**需求描述**: 车间主管应能通过电子看板实时掌握生产进度、设备状态和质量异常

**验收标准**:
- [ ] 数据刷新频率不超过1分钟
- [ ] 支持订单进度可视化展示
- [ ] 设备状态地图实时更新
- [ ] 质量异常滚动播报
- [ ] 安灯呼叫自动触发和记录

**优先级**: 中  
**依赖**: 实时数据采集系统

### 3.3 全生命周期质量追溯 (F-PD-3)

#### 需求 3.3.1: UID生成与绑定
**需求描述**: 系统应能在玻璃被切割时，自动为每一片独立的成品或半成品生成全局唯一的身份ID

**验收标准**:
- [ ] UID全局唯一性保证100%
- [ ] 支持自动预生成和标签打印
- [ ] 破损处理和作废功能完整
- [ ] 标签打印成功率达到99%
- [ ] 支持批量操作和单个操作

**优先级**: 高  
**依赖**: 标签打印机、切割优化系统

#### 需求 3.3.2: 扫码关联
**需求描述**: 操作员应能通过扫描UID码，将当前操作信息自动关联到产品档案中

**验收标准**:
- [ ] 扫码响应时间不超过1秒
- [ ] 支持工序校验和重复扫描检测
- [ ] 自动关联工艺参数和辅料信息
- [ ] 异常处理和错误提示清晰
- [ ] 支持IPQC/FQC检验关联

**优先级**: 高  
**依赖**: 扫码设备、工艺路线数据

#### 需求 3.3.3: 追溯查询
**需求描述**: 质量经理应能通过追溯平台，输入UID立即获得完整生命周期记录

**验收标准**:
- [ ] 查询响应时间不超过3秒
- [ ] 支持时间轴可视化展示
- [ ] 工艺参数曲线图内嵌显示
- [ ] 支持PDF报告生成和Excel导出
- [ ] 数据权限控制完整

**优先级**: 高  
**依赖**: 历史数据存储、报告生成服务

### 3.4 设备物联与数据驱动决策 (F-PD-4)

#### 需求 3.4.1: 设备集成管理
**需求描述**: 系统应提供灵活的、分层式的设备集成方案

**验收标准**:
- [ ] 支持设备资产信息管理
- [ ] 数据点位表配置功能完整
- [ ] 设备连接状态实时监控
- [ ] 离线告警及时触发（3分钟内）
- [ ] 支持多种通信协议

**优先级**: 中  
**依赖**: 设备通信协议、时序数据库

#### 需求 3.4.2: OEE监控分析
**需求描述**: 生产总监应能实时监控全厂核心设备的OEE，并能下钻分析效率损失根因

**验收标准**:
- [ ] OEE计算准确率达到95%
- [ ] 支持多维度筛选和下钻
- [ ] 损失分析柏拉图自动生成
- [ ] 数据可视化响应时间不超过5秒
- [ ] 支持历史趋势分析

**优先级**: 中  
**依赖**: 设备状态数据、产量数据、质量数据

---

## 4. 数据模型

### 4.1 核心实体关系图

```mermaid
erDiagram
    SALES_ORDER ||--|{ PRODUCTION_ORDER : "generates"
    PRODUCTION_ORDER ||--|{ WORK_ORDER_TASK : "decomposes to"
    WORK_ORDER_TASK ||--|{ TASK_REPORT_LOG : "has"
    WORK_ORDER_TASK ||--o{ UID_TRACE_LOG : "processed by"
    PRODUCT_UID ||--o{ UID_TRACE_LOG : "is"
    DEVICE ||--|{ WORK_ORDER_TASK : "is assigned to"
    USER ||--|{ TASK_REPORT_LOG : "reports"
```

### 4.2 关键实体字段定义

#### 工序任务 (Work_Order_Task)
| 字段名 | 数据类型 | 必填 | 描述 |
|--------|----------|------|------|
| task_id | string | 是 | 任务唯一ID |
| prod_order_id | string | 是 | 关联生产订单 |
| sequence | int | 是 | 工序号 |
| operation_name | string | 是 | 工序名称 |
| work_center_id | string | 是 | 工作中心ID |
| scheduled_start_time | datetime | 否 | 计划开始时间 |
| scheduled_end_time | datetime | 否 | 计划结束时间 |
| actual_start_time | datetime | 否 | 实际开始时间 |
| actual_end_time | datetime | 否 | 实际结束时间 |
| status | string | 是 | 任务状态 |

#### 产品唯一ID (Product_UID)
| 字段名 | 数据类型 | 必填 | 描述 |
|--------|----------|------|------|
| uid | string | 是 | 全局唯一身份ID |
| product_id | string | 是 | 关联产品主数据 |
| source_task_id | string | 是 | 源头任务ID |
| status | string | 是 | UID状态 |
| creation_time | datetime | 是 | 创建时间 |
| last_scan_time | datetime | 否 | 最后扫描时间 |

---

## 5. 系统集成

### 5.1 与ERP系统集成
- **ERP → MES**: 销售订单、物料主数据、库存数据
- **MES → ERP**: 生产进度反馈、精确实耗上报

### 5.2 与PLM系统集成
- **PLM → MES**: 工艺文件、技术文档、CAM程序

### 5.3 与WMS系统集成
- **双向集成**: 物料配送状态、完工入库信息

---

## 6. 非功能性需求

### 6.1 性能要求
- 系统响应时间：界面操作 < 2秒，复杂查询 < 5秒
- 并发用户数：支持100个并发用户
- 数据处理能力：支持1000个工序任务的排程计算

### 6.2 可用性要求
- 系统可用性：99.5%
- 故障恢复时间：< 4小时
- 数据备份：每日自动备份

### 6.3 安全要求
- 用户认证：支持多种认证方式
- 数据加密：敏感数据传输和存储加密
- 权限控制：基于角色的访问控制

---

## 7. 验收标准

### 7.1 功能验收
- [ ] 所有高优先级需求100%实现
- [ ] 所有验收标准通过测试
- [ ] 用户场景端到端测试通过

### 7.2 性能验收
- [ ] 性能指标达到要求
- [ ] 压力测试通过
- [ ] 稳定性测试通过

### 7.3 集成验收
- [ ] 与外部系统集成测试通过
- [ ] 数据一致性验证通过
- [ ] 业务流程端到端验证通过

---

## 附录

### A. 风险评估
| 风险项 | 影响程度 | 发生概率 | 缓解措施 |
|--------|----------|----------|----------|
| APS算法性能不达标 | 高 | 中 | 提前进行算法验证和优化 |
| 设备集成复杂度超预期 | 中 | 高 | 分阶段实施，优先核心设备 |
| 用户接受度不高 | 中 | 中 | 加强用户培训和变更管理 |

### B. 项目里程碑
| 里程碑 | 计划完成时间 | 关键交付物 |
|--------|--------------|------------|
| 需求确认 | Week 2 | 需求规格书 |
| 系统设计 | Week 6 | 系统设计文档 |
| 开发完成 | Week 16 | 系统功能实现 |
| 测试完成 | Week 20 | 测试报告 |
| 上线部署 | Week 22 | 生产环境部署 |

---

**文档版本控制**
- v2.2: 质量优化版，按照审计建议进行全面改进
- v2.1: 高保真原型设计版
- v2.0: 初始架构设计稿
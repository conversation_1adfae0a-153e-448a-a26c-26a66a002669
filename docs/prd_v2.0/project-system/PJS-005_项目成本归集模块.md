# 功能模块规格说明书：项目成本归集模块

- **模块ID**: PJS-005
- **所属子系统**: 项目管理子系统(Project System)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 项目经理, **I want to** 实时查看项目成本归集情况, **so that** 控制项目成本在预算范围内。
- **As a** 财务人员, **I want to** 自动归集各类项目费用, **so that** 准确核算项目成本。
- **As a** 项目主管, **I want to** 分析项目成本构成, **so that** 优化成本结构和控制策略。
- **As a** 成本会计, **I want to** 按项目结构分摊成本, **so that** 精确计算各部分成本。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 项目主数据已创建
- 项目结构已分解
- 成本科目已设置
- 相关业务单据已审核

### 核心流程

#### 2.1 成本自动归集流程
1. 系统定时扫描各业务模块的成本数据
2. 根据项目编码自动识别项目相关费用
3. 按成本类型分类归集到对应项目
4. 更新项目成本明细和汇总数据
5. 生成成本归集日志和异常报告
6. 通知相关人员成本更新情况

#### 2.2 成本分摊流程
1. 识别需要分摊的间接成本
2. 选择合适的分摊基础和方法
3. 计算各项目的分摊比例
4. 执行成本分摊并记录分摊明细
5. 更新项目成本数据
6. 生成分摊报告和凭证

#### 2.3 成本调整流程
1. 发现成本归集错误或需要调整
2. 填写成本调整申请单
3. 说明调整原因和依据
4. 提交审批流程
5. 审批通过后执行调整
6. 更新成本数据并记录调整历史

### 后置条件
- 项目成本数据准确
- 成本归集完整及时
- 成本分析报告可用
- 成本控制有效

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：项目成本归集管理页面
### 页面目标：提供项目成本自动归集、分摊和分析功能

### 信息架构：
- **顶部区域**：包含 项目选择, 时间范围, 成本视图, 刷新同步
- **左侧区域**：包含 成本科目树, 筛选条件, 快速统计
- **中间区域**：包含 成本明细, 归集规则, 分摊设置
- **右侧区域**：包含 成本分析, 趋势图表, 预警信息

### 交互逻辑与状态：

#### **项目和时间选择区域**
- **项目选择：**
  - **项目下拉：** 下拉选择器，选择要查看的项目
  - **项目信息：** 显示项目基本信息和成本概览
  - **多项目对比：** 支持选择多个项目进行成本对比
- **时间范围：**
  - **会计期间：** 下拉选择会计年度和期间
  - **自定义范围：** 日期范围选择器
  - **预设范围：** 本月/本季度/本年度快捷选择
  - **实时/历史：** 切换查看实时数据或历史数据

#### **成本科目树区域**
- **科目展示：**
  - **科目树：** 树形结构显示成本科目层级
  - **科目编码：** 显示科目编码和名称
  - **科目金额：** 显示各科目归集金额
  - **占比显示：** 显示各科目占总成本比例
- **科目操作：**
  - **展开/折叠：** 点击展开或折叠科目子级
  - **科目筛选：** 复选框选择要显示的科目
  - **金额排序：** 按金额大小排序科目
  - **科目搜索：** 输入框搜索科目名称

#### **成本明细列表**
- **明细表头：**
  - **业务日期：** 显示业务发生日期
  - **单据类型：** 显示单据类型（采购/生产/人工等）
  - **单据编号：** 显示源单据编号，可点击查看
  - **成本科目：** 显示归集的成本科目
  - **成本金额：** 显示成本金额，右对齐
  - **项目节点：** 显示归集到的项目节点
  - **归集状态：** 显示归集状态（已归集/待归集/异常）
- **明细筛选：**
  - **单据类型：** 下拉多选筛选单据类型
  - **成本科目：** 下拉选择成本科目
  - **金额范围：** 数字范围输入筛选金额
  - **归集状态：** 下拉选择归集状态
- **批量操作：**
  - **批量归集：** 批量执行成本归集
  - **批量调整：** 批量调整归集科目
  - **导出明细：** 导出成本明细到Excel
  - **重新归集：** 重新执行归集规则

#### **成本归集规则设置**
- **规则列表：**
  - **规则名称：** 显示归集规则名称
  - **适用范围：** 显示规则适用的业务范围
  - **归集科目：** 显示目标成本科目
  - **规则状态：** 显示规则启用状态
- **规则配置：**
  - **规则名称：** 输入框，规则名称
  - **触发条件：** 设置规则触发条件
  - **匹配字段：** 选择用于匹配的字段
  - **匹配规则：** 设置字段匹配规则（等于/包含/正则）
- **归集设置：**
  - **目标项目：** 设置归集到的目标项目
  - **目标科目：** 设置归集到的成本科目
  - **归集比例：** 设置归集比例（100%或按比例）
  - **优先级：** 设置规则执行优先级

#### **成本分摊管理**
- **分摊方案：**
  - **方案名称：** 显示分摊方案名称
  - **分摊基础：** 显示分摊基础（面积/产值/工时等）
  - **适用项目：** 显示适用的项目范围
  - **执行状态：** 显示方案执行状态
- **分摊设置：**
  - **分摊科目：** 选择需要分摊的成本科目
  - **分摊基础：** 选择分摊基础类型
  - **分摊比例：** 设置各项目分摊比例
  - **分摊周期：** 设置分摊执行周期
- **分摊执行：**
  - **手动分摊：** 手动执行分摊计算
  - **自动分摊：** 设置自动分摊规则
  - **分摊预览：** 预览分摊结果
  - **分摊确认：** 确认执行分摊

#### **成本分析图表**
- **成本构成：**
  - **饼图：** 显示成本科目构成比例
  - **柱状图：** 显示各科目成本金额对比
  - **堆叠图：** 显示成本构成变化趋势
  - **树状图：** 显示成本科目层级结构
- **趋势分析：**
  - **成本趋势：** 折线图显示成本变化趋势
  - **预算对比：** 对比实际成本与预算成本
  - **同期对比：** 对比不同期间成本情况
  - **项目对比：** 对比不同项目成本水平
- **成本指标：**
  - **成本率：** 显示各类成本占比
  - **单位成本：** 显示单位面积/产值成本
  - **成本效率：** 显示成本控制效率指标
  - **成本预测：** 基于趋势预测未来成本

#### **成本预警监控**
- **预警规则：**
  - **预算超支：** 成本超出预算时预警
  - **异常波动：** 成本异常波动时预警
  - **归集异常：** 成本归集异常时预警
  - **科目异常：** 特定科目异常时预警
- **预警信息：**
  - **预警级别：** 显示预警级别（严重/警告/提醒）
  - **预警内容：** 显示具体预警信息
  - **影响金额：** 显示预警涉及的金额
  - **处理建议：** 显示系统建议的处理措施
- **预警处理：**
  - **确认预警：** 确认已知晓预警信息
  - **处理记录：** 记录预警处理过程
  - **预警关闭：** 问题解决后关闭预警
  - **预警统计：** 统计预警发生频率和处理情况

#### **成本调整管理**
- **调整申请：**
  - **调整类型：** 选择调整类型（科目调整/金额调整/项目调整）
  - **调整原因：** 文本域，说明调整原因
  - **调整金额：** 数字输入框，调整金额
  - **调整说明：** 文本域，详细调整说明
- **调整审批：**
  - **申请信息：** 显示调整申请详细信息
  - **审批意见：** 文本域，填写审批意见
  - **审批结果：** 单选按钮，同意/拒绝
  - **审批历史：** 显示调整申请审批历史
- **调整执行：**
  - **调整确认：** 确认执行调整操作
  - **调整记录：** 记录调整操作详情
  - **数据更新：** 更新相关成本数据
  - **通知发送：** 通知相关人员调整结果

#### **成本报表生成**
- **报表类型：**
  - **成本明细表：** 详细的成本归集明细
  - **成本汇总表：** 按科目汇总的成本报表
  - **成本分析表：** 成本分析和对比报表
  - **成本预警表：** 成本预警和异常报表
- **报表参数：**
  - **报表期间：** 选择报表统计期间
  - **项目范围：** 选择包含的项目范围
  - **科目范围：** 选择包含的成本科目
  - **报表格式：** 选择报表输出格式
- **报表输出：**
  - **在线查看：** 在线查看报表内容
  - **PDF导出：** 导出PDF格式报表
  - **Excel导出：** 导出Excel格式报表
  - **定时发送：** 设置报表定时发送

### 数据校验规则：

#### **成本金额**
- **校验规则：** 成本金额必须大于0
- **错误提示文案：** "成本金额必须大于0"

#### **归集规则**
- **校验规则：** 归集规则不能冲突
- **错误提示文案：** "归集规则与现有规则冲突，请检查"

#### **分摊比例**
- **校验规则：** 分摊比例总和必须等于100%
- **错误提示文案：** "分摊比例总和必须等于100%"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **成本数据**:
  - **业务单据ID (document_id)**: String, 必填, 引用源单据
  - **成本金额 (cost_amount)**: Decimal, 必填, 大于0
  - **成本科目 (cost_subject)**: String, 必填, 引用成本科目
  - **项目编码 (project_code)**: String, 必填, 引用项目
- **归集规则**:
  - **规则名称 (rule_name)**: String, 必填, 最大50字符
  - **匹配条件 (match_condition)**: String, 必填, JSON格式

### 展示数据
- **成本明细**: 详细的成本归集记录
- **成本汇总**: 按科目和项目汇总的成本
- **成本分析**: 成本构成、趋势、对比分析
- **预警信息**: 成本预警和异常信息

### 空状态/零数据
- **无成本数据**: 显示"暂无成本数据"
- **无归集规则**: 显示"暂无归集规则，请先设置归集规则"
- **无预警信息**: 显示"当前无成本预警"

### API接口
- **成本查询**: GET /api/projects/{id}/costs
- **成本归集**: POST /api/projects/{id}/cost-allocation
- **归集规则**: POST /api/projects/cost-rules
- **成本分摊**: POST /api/projects/{id}/cost-distribution
- **成本调整**: POST /api/projects/cost-adjustments

## 5. 异常与边界处理 (Error & Edge Cases)

### **归集规则冲突**
- **提示信息**: "归集规则与现有规则冲突，请检查规则设置"
- **用户操作**: 显示冲突规则详情和修改建议

### **成本数据异常**
- **提示信息**: "检测到异常成本数据，请核实数据来源"
- **用户操作**: 显示异常数据详情和处理选项

### **分摊计算失败**
- **提示信息**: "成本分摊计算失败，请检查分摊基础数据"
- **用户操作**: 提供分摊基础数据检查和修复工具

### **权限不足**
- **提示信息**: "您没有权限执行成本调整操作"
- **用户操作**: 显示所需权限和申请流程

### **数据同步延迟**
- **提示信息**: "成本数据同步中，可能不是最新数据"
- **用户操作**: 显示同步进度和手动刷新选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 成本自动归集准确，规则配置灵活
- [ ] 成本分摊计算正确，支持多种分摊方式
- [ ] 成本分析图表清晰，数据准确
- [ ] 成本预警及时有效，处理流程完整
- [ ] 成本调整流程规范，审批控制严格
- [ ] 数据实时性好，延迟<10分钟
- [ ] 支持大数据量处理（百万级记录）
- [ ] 成本报表功能完善，格式规范
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 数据准确性高，异常处理完善
- [ ] 与财务系统集成正常

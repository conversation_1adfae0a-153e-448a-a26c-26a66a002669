# 功能模块规格说明书：工位任务执行模块

- **模块ID**: MES-004
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 车间操作员, **I want to** 在工位终端查看当前任务, **so that** 了解需要执行的生产作业。
- **As a** 车间操作员, **I want to** 查看数字化SOP指导, **so that** 按标准流程执行操作。
- **As a** 车间操作员, **I want to** 记录工艺参数和质量数据, **so that** 确保产品质量可追溯。
- **As a** 车间主管, **I want to** 实时监控工位执行状态, **so that** 及时发现和处理异常。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 工位终端设备正常运行
- 生产任务已成功下发到工位
- 操作员已完成身份认证
- 相关物料和工具准备就绪

### 核心流程

#### 2.1 任务接收确认流程
1. 工位终端接收新任务推送
2. 显示任务通知和基本信息
3. 操作员确认接收任务
4. 验证物料和工具可用性
5. 检查设备状态和工艺参数
6. 更新任务状态为"准备中"

#### 2.2 任务执行准备流程
1. 查看任务详细信息和要求
2. 加载对应的数字化SOP
3. 检查工艺参数设置
4. 确认质量检验标准
5. 准备必要的物料和工具
6. 开始任务执行

#### 2.3 数字化SOP执行流程
1. 按步骤显示作业指导内容
2. 包含文字说明、图片、视频
3. 操作员确认每个步骤完成
4. 记录关键工艺参数
5. 拍照记录关键操作过程
6. 完成质量检验和记录

#### 2.4 任务完成报告流程
1. 确认所有工序步骤完成
2. 填写完工数量和质量状态
3. 上传质量检验数据
4. 标记异常情况和处理方式
5. 提交任务完成报告
6. 更新任务状态为"已完成"

### 后置条件
- 任务执行过程完整记录
- 工艺参数和质量数据准确
- 异常情况得到妥善处理
- 下一工序可以正常接收

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工位任务执行界面
### 页面目标：提供简洁高效的工位作业执行界面

### 信息架构：
- **顶部区域**：包含 工位信息, 操作员信息, 系统状态
- **中间区域**：包含 当前任务卡片, SOP执行面板, 参数录入区域
- **底部区域**：包含 操作按钮, 状态指示, 帮助信息

### 交互逻辑与状态：

#### **工位信息栏**
- **工位标识：**
  - **工位编号：** 大号字体显示，如"工位A01"
  - **工位名称：** 显示工位功能描述
  - **设备状态：** 绿色/红色圆点表示设备正常/异常
- **操作员信息：**
  - **操作员姓名：** 显示当前登录操作员
  - **技能等级：** 显示操作员技能级别
  - **登录时间：** 显示登录时间
- **系统状态：**
  - **网络连接：** 显示网络连接状态
  - **数据同步：** 显示数据同步状态
  - **当前时间：** 实时显示系统时间

#### **当前任务卡片**
- **任务基本信息：**
  - **任务编号：** 大号字体突出显示
  - **产品名称：** 显示产品名称和规格
  - **加工数量：** 突出显示数量和单位
  - **计划时间：** 显示开始和结束时间
- **任务状态指示：**
  - **待开始：** 灰色边框，"待开始"标签
  - **执行中：** 蓝色边框，"执行中"标签，带进度条
  - **暂停中：** 黄色边框，"暂停中"标签
  - **已完成：** 绿色边框，"已完成"标签
- **进度显示：**
  - **步骤进度：** 显示"第X步/共Y步"
  - **时间进度：** 显示已用时间和剩余时间
  - **完成百分比：** 圆形进度条显示完成度

#### **任务操作按钮组**
- **开始任务按钮：**
  - **默认状态：** 绿色背景(#52C41A)，白色文字"开始任务"
  - **执行中状态：** 蓝色背景，显示"暂停任务"
  - **暂停状态：** 绿色背景，显示"继续任务"
- **完成任务按钮：** 绿色边框按钮，任务完成时启用
- **异常报告按钮：** 红色边框按钮，报告异常情况
- **帮助按钮：** 灰色边框按钮，查看帮助信息

#### **SOP执行面板**
- **步骤导航：**
  - **步骤列表：** 左侧显示所有步骤，当前步骤高亮
  - **步骤编号：** 圆形数字标识，完成步骤显示绿色勾选
  - **步骤名称：** 显示步骤简要描述
- **步骤内容区：**
  - **文字说明：** 详细的操作说明文字
  - **图片展示：** 操作示意图，支持放大查看
  - **视频指导：** 操作视频，支持播放控制
  - **注意事项：** 红色文字突出显示安全注意事项
- **步骤操作：**
  - **上一步按钮：** 返回上一个步骤
  - **下一步按钮：** 进入下一个步骤
  - **完成步骤：** 确认当前步骤完成
  - **跳过步骤：** 特殊情况下跳过步骤

#### **参数录入区域**
- **工艺参数表格：**
  - **参数名称：** 显示参数名称和单位
  - **标准值：** 显示标准值或范围
  - **实测值：** 输入框录入实际测量值
  - **状态指示：** 绿色/红色表示合格/不合格
- **质量检验项：**
  - **检验项目：** 显示需要检验的项目
  - **检验标准：** 显示检验标准和要求
  - **检验结果：** 单选按钮选择合格/不合格
  - **备注说明：** 文本框填写备注信息
- **数据录入辅助：**
  - **扫码录入：** 扫描二维码自动填入数据
  - **语音录入：** 语音输入数值数据
  - **拍照记录：** 拍照记录关键操作过程

#### **参数录入控件**
- **数值输入框：**
  - **实时校验：** 输入时实时校验数值范围
  - **单位显示：** 输入框右侧显示单位
  - **错误提示：** 超出范围时红色边框和提示
- **下拉选择框：** 预设选项的参数选择
- **复选框组：** 多选项的质量检验项
- **文本区域：** 备注和说明信息输入
- **文件上传：** 上传图片和文档附件

#### **异常报告对话框**
- **异常类型选择：**
  - **设备异常：** 设备故障或异常
  - **物料异常：** 物料缺失或质量问题
  - **工艺异常：** 工艺参数异常
  - **质量异常：** 产品质量不合格
  - **其他异常：** 其他类型异常
- **异常描述：**
  - **异常现象：** 文本框描述异常现象
  - **发生时间：** 自动记录异常发生时间
  - **影响程度：** 选择异常影响程度
- **处理措施：**
  - **临时处理：** 描述临时处理措施
  - **需要支援：** 是否需要技术支援
  - **停机等待：** 是否需要停机等待处理
- **附件上传：** 上传异常现场照片或视频

#### **任务完成对话框**
- **完工信息确认：**
  - **完工数量：** 输入实际完工数量
  - **合格数量：** 输入合格产品数量
  - **不合格数量：** 输入不合格产品数量
  - **废品数量：** 输入废品数量
- **质量状态确认：**
  - **整体质量：** 选择合格/不合格
  - **质量等级：** 选择质量等级
  - **质量备注：** 填写质量相关备注
- **时间信息：**
  - **实际开始时间：** 显示实际开始时间
  - **实际结束时间：** 自动记录当前时间
  - **实际工时：** 自动计算实际用时
- **下一工序：** 选择产品流向的下一工序

#### **帮助信息面板**
- **操作指南：** 显示界面操作指南
- **常见问题：** 列出常见问题和解答
- **联系支持：** 提供技术支持联系方式
- **快捷键：** 显示键盘快捷键说明

### 数据校验规则：

#### **工艺参数**
- **校验规则：** 数值必须在标准范围内
- **错误提示文案：** "参数值超出标准范围，请重新测量"

#### **完工数量**
- **校验规则：** 完工数量不能超过计划数量
- **错误提示文案：** "完工数量不能超过计划数量"

#### **质量数据**
- **校验规则：** 质量检验项必须全部填写
- **错误提示文案：** "请完成所有质量检验项"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **任务信息**:
  - **任务ID (task_id)**: String, 必填
  - **工艺参数 (process_params)**: Object, 工艺参数值
  - **质量数据 (quality_data)**: Object, 质量检验结果
  - **完工信息 (completion_info)**: Object, 完工数量和状态
- **操作记录**:
  - **操作员ID (operator_id)**: String, 必填
  - **操作时间 (operation_time)**: DateTime, 自动记录
  - **操作类型 (operation_type)**: Enum, 操作类型

### 展示数据
- **任务详情**: 任务信息、SOP内容、工艺要求
- **执行进度**: 步骤进度、时间进度、完成状态
- **参数录入**: 工艺参数、质量数据、检验结果
- **历史记录**: 操作历史、异常记录、完成记录

### 空状态/零数据
- **无当前任务**: 显示"暂无分配任务，请等待任务下发"
- **SOP缺失**: 显示"作业指导书缺失，请联系工艺人员"
- **网络断开**: 显示"网络连接中断，数据可能无法同步"

### API接口
- **获取当前任务**: GET /api/stations/{id}/current-task
- **提交工艺参数**: POST /api/tasks/{id}/process-data
- **提交完工报告**: POST /api/tasks/{id}/completion
- **报告异常**: POST /api/tasks/{id}/exception

## 5. 异常与边界处理 (Error & Edge Cases)

### **网络连接中断**
- **提示信息**: "网络连接中断，数据将在恢复后自动同步"
- **用户操作**: 本地缓存数据，网络恢复后自动上传

### **设备传感器异常**
- **提示信息**: "设备传感器异常，请手动录入参数"
- **用户操作**: 提供手动录入选项，标记数据来源

### **SOP内容缺失**
- **提示信息**: "作业指导书缺失，请联系工艺人员补充"
- **用户操作**: 提供联系方式，允许继续执行并标记

### **参数超出范围**
- **提示信息**: "工艺参数超出标准范围，请检查设备状态"
- **用户操作**: 高亮异常参数，提供调整建议

### **任务执行超时**
- **提示信息**: "任务执行时间超出计划，请确认是否继续"
- **用户操作**: 提供继续/暂停/报告异常选项

### **数据保存失败**
- **提示信息**: "数据保存失败，请检查网络连接"
- **用户操作**: 本地缓存数据，提供重试功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 工位终端清晰显示当前任务信息
- [ ] 数字化SOP支持文字、图片、视频展示
- [ ] 工艺参数录入支持手动和自动采集
- [ ] 质量检验数据完整记录和校验
- [ ] 任务执行进度实时更新和显示
- [ ] 异常情况快速报告和处理
- [ ] 完工信息准确提交和确认
- [ ] 支持离线操作和数据同步
- [ ] 界面适配工位终端屏幕尺寸
- [ ] 操作简单直观，减少误操作
- [ ] 任务切换响应时间<3秒
- [ ] 数据录入自动保存，防止丢失
- [ ] 所有界面元素符合工业设计规范
- [ ] 支持触摸操作和手势控制

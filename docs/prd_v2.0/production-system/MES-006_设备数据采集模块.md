# 功能模块规格说明书：设备数据采集模块

- **模块ID**: MES-006
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 设备工程师, **I want to** 实时监控设备运行状态, **so that** 及时发现设备异常和故障。
- **As a** 质量工程师, **I want to** 自动采集关键工艺参数, **so that** 确保产品质量的一致性。
- **As a** 生产计划员, **I want to** 获取设备产能数据, **so that** 优化生产排程和资源配置。
- **As a** 车间主管, **I want to** 查看设备利用率报告, **so that** 评估设备效率和改进方向。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备已安装数据采集传感器
- 数据采集网关正常运行
- 设备通信协议配置正确
- 数据采集规则已设置

### 核心流程

#### 2.1 设备连接建立流程
1. 扫描网络中的设备节点
2. 验证设备身份和通信协议
3. 建立稳定的数据通信连接
4. 配置数据采集参数和频率
5. 测试数据传输的稳定性
6. 记录设备连接状态和信息

#### 2.2 实时数据采集流程
1. 按设定频率采集设备数据
2. 验证数据的完整性和有效性
3. 进行数据预处理和格式转换
4. 关联数据到具体的生产任务
5. 存储原始数据和处理后数据
6. 触发实时数据分析和预警

#### 2.3 数据质量监控流程
1. 实时监控数据采集状态
2. 检测数据异常和通信中断
3. 验证数据的合理性和一致性
4. 识别传感器故障和漂移
5. 自动修复或标记异常数据
6. 生成数据质量报告

#### 2.4 设备状态分析流程
1. 分析设备运行状态数据
2. 计算设备利用率和效率
3. 识别设备异常和故障征兆
4. 预测设备维护需求
5. 生成设备状态报告
6. 触发预警和维护建议

### 后置条件
- 设备数据完整准确采集
- 数据质量符合要求
- 异常情况及时发现处理
- 设备状态清晰可见

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备数据采集监控页面
### 页面目标：提供全面的设备数据采集监控和管理界面

### 信息架构：
- **顶部区域**：包含 采集状态总览, 设备连接监控, 数据质量指标
- **中间区域**：包含 设备列表, 实时数据图表, 参数配置面板
- **底部区域**：包含 历史数据查询, 报警信息, 统计分析

### 交互逻辑与状态：

#### **采集状态总览**
- **总体状态指示：**
  - **正常采集：** 绿色指示灯，"数据采集正常"
  - **部分异常：** 黄色指示灯，"X台设备异常"
  - **严重异常：** 红色指示灯，"多台设备离线"
- **关键指标卡片：**
  - **在线设备数：** 绿色数字，显示在线设备数量
  - **采集点数：** 蓝色数字，显示总采集点数
  - **数据完整率：** 百分比显示，数据完整性
  - **异常报警数：** 红色数字，当前异常数量

#### **设备连接监控**
- **设备网络拓扑图：**
  - **网关节点：** 中心节点，显示网关状态
  - **设备节点：** 周围节点，显示各设备状态
  - **连接线：** 绿色/红色表示连接正常/异常
  - **数据流：** 动画显示数据传输方向
- **连接状态列表：**
  - **设备名称：** 显示设备编号和名称
  - **IP地址：** 显示设备网络地址
  - **连接状态：** 绿色/红色圆点表示状态
  - **信号强度：** 显示通信信号强度
  - **最后通信：** 显示最后通信时间

#### **设备列表面板**
- **设备卡片布局：** 网格布局显示各设备
- **设备卡片内容：**
  - **设备名称：** 大号字体显示设备编号
  - **设备类型：** 显示设备类型和型号
  - **运行状态：** 彩色圆点和文字说明
  - **关键参数：** 显示2-3个关键参数值
  - **采集频率：** 显示数据采集频率
  - **操作按钮：** 查看详情、参数配置、重启采集

#### **设备状态标识**
- **运行正常：** 绿色卡片边框，绿色状态点
- **运行异常：** 黄色卡片边框，黄色状态点
- **设备故障：** 红色卡片边框，红色状态点
- **通信中断：** 灰色卡片边框，灰色状态点
- **维护模式：** 蓝色卡片边框，蓝色状态点

#### **实时数据图表**
- **多参数趋势图：**
  - **时间轴：** 水平时间轴，支持缩放
  - **参数轴：** 多个Y轴显示不同参数
  - **趋势线：** 不同颜色显示各参数趋势
  - **阈值线：** 虚线显示参数上下限
  - **异常标记：** 红色点标记异常数据
- **实时仪表盘：**
  - **圆形仪表：** 显示关键参数当前值
  - **数值显示：** 大号数字显示精确值
  - **状态指示：** 绿色/红色表示正常/异常
  - **单位标识：** 显示参数单位

#### **参数配置面板**
- **采集参数设置：**
  - **采集频率：** 下拉选择采集间隔
  - **数据精度：** 设置数据保留小数位
  - **存储策略：** 选择数据存储策略
  - **压缩算法：** 选择数据压缩方式
- **阈值设置：**
  - **上限值：** 输入框设置参数上限
  - **下限值：** 输入框设置参数下限
  - **报警延迟：** 设置报警触发延迟
  - **报警级别：** 选择报警严重程度
- **通信配置：**
  - **协议类型：** 选择通信协议
  - **端口设置：** 设置通信端口
  - **超时时间：** 设置通信超时
  - **重试次数：** 设置连接重试次数

#### **数据质量监控**
- **质量指标展示：**
  - **数据完整率：** 百分比显示数据完整性
  - **数据准确率：** 百分比显示数据准确性
  - **通信成功率：** 百分比显示通信成功率
  - **异常数据率：** 百分比显示异常数据比例
- **质量趋势图：** 显示数据质量随时间变化
- **异常统计：** 统计各类异常的发生次数

#### **历史数据查询**
- **查询条件设置：**
  - **时间范围：** 日期时间选择器
  - **设备选择：** 多选下拉框选择设备
  - **参数选择：** 复选框选择查询参数
  - **数据类型：** 选择原始/处理后数据
- **查询结果展示：**
  - **数据表格：** 表格显示查询结果
  - **图表展示：** 图表显示数据趋势
  - **统计信息：** 显示数据统计信息
  - **导出功能：** 导出数据到Excel/CSV

#### **报警信息面板**
- **实时报警列表：**
  - **报警时间：** 显示报警发生时间
  - **设备名称：** 显示报警设备
  - **报警类型：** 显示报警类型和级别
  - **参数值：** 显示异常参数值
  - **处理状态：** 显示报警处理状态
- **报警级别标识：**
  - **严重：** 红色标签，需要立即处理
  - **警告：** 橙色标签，需要关注
  - **提示：** 蓝色标签，一般性提示
- **报警处理：**
  - **确认报警：** 确认已知晓报警
  - **处理报警：** 标记报警已处理
  - **忽略报警：** 忽略误报警信息

#### **设备详情对话框**
- **基本信息：**
  - **设备编号：** 显示设备唯一编号
  - **设备名称：** 显示设备名称
  - **设备型号：** 显示设备型号规格
  - **安装位置：** 显示设备安装位置
  - **投用时间：** 显示设备投用时间
- **运行状态：**
  - **当前状态：** 显示设备当前运行状态
  - **运行时长：** 显示累计运行时间
  - **故障次数：** 显示历史故障次数
  - **维护记录：** 显示最近维护记录
- **采集配置：**
  - **采集点列表：** 显示所有采集点
  - **采集频率：** 显示各点采集频率
  - **数据范围：** 显示参数正常范围
  - **报警设置：** 显示报警阈值设置

#### **统计分析面板**
- **设备利用率分析：**
  - **时间维度：** 按小时/天/周/月统计
  - **利用率图表：** 柱状图显示利用率
  - **对比分析：** 不同设备利用率对比
- **故障分析：**
  - **故障统计：** 统计故障类型和次数
  - **故障趋势：** 显示故障发生趋势
  - **MTBF分析：** 平均故障间隔时间
  - **MTTR分析：** 平均修复时间
- **效率分析：**
  - **产能分析：** 设备实际产能统计
  - **效率趋势：** 设备效率变化趋势
  - **瓶颈识别：** 识别生产瓶颈设备

### 数据校验规则：

#### **采集参数**
- **校验规则：** 采集频率必须在合理范围内（1秒-1小时）
- **错误提示文案：** "采集频率设置超出合理范围"

#### **阈值设置**
- **校验规则：** 上限值必须大于下限值
- **错误提示文案：** "参数上限值必须大于下限值"

#### **通信配置**
- **校验规则：** IP地址格式正确，端口号在有效范围内
- **错误提示文案：** "通信配置参数格式错误"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **设备配置**:
  - **设备ID (device_id)**: String, 必填, 唯一标识
  - **采集频率 (collection_frequency)**: Number, 必填, 秒为单位
  - **参数阈值 (parameter_thresholds)**: Object, 上下限值
  - **通信配置 (communication_config)**: Object, 协议和地址
- **采集数据**:
  - **时间戳 (timestamp)**: DateTime, 必填
  - **参数值 (parameter_values)**: Object, 各参数数值
  - **数据质量 (data_quality)**: Enum, [正常/异常/缺失]

### 展示数据
- **设备状态**: 连接状态、运行状态、采集状态
- **实时数据**: 参数值、趋势图、仪表盘显示
- **历史数据**: 时间序列数据、统计分析结果
- **报警信息**: 报警列表、处理状态、统计信息

### 空状态/零数据
- **无设备连接**: 显示"暂无设备连接，请检查网络配置"
- **无采集数据**: 显示"暂无采集数据，请检查设备状态"
- **通信中断**: 显示"设备通信中断，正在尝试重连"

### API接口
- **获取设备列表**: GET /api/devices
- **设备数据采集**: POST /api/devices/{id}/collect
- **查询历史数据**: GET /api/devices/{id}/history
- **设备配置更新**: PUT /api/devices/{id}/config

## 5. 异常与边界处理 (Error & Edge Cases)

### **设备通信中断**
- **提示信息**: "设备通信中断，正在尝试重新连接"
- **用户操作**: 显示重连进度，提供手动重连选项

### **传感器故障**
- **提示信息**: "传感器数据异常，可能存在硬件故障"
- **用户操作**: 标记异常数据，通知维护人员

### **数据超出阈值**
- **提示信息**: "参数值超出正常范围，请检查设备状态"
- **用户操作**: 触发报警，记录异常事件

### **存储空间不足**
- **提示信息**: "数据存储空间不足，请清理历史数据"
- **用户操作**: 提供数据清理工具，设置自动清理策略

### **网络延迟过高**
- **提示信息**: "网络延迟过高，数据采集可能不及时"
- **用户操作**: 调整采集频率，优化网络配置

### **时钟同步异常**
- **提示信息**: "设备时钟同步异常，数据时间戳可能不准确"
- **用户操作**: 提供时钟同步功能，校正时间戳

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多种设备通信协议（Modbus、OPC UA等）
- [ ] 实时数据采集延迟<5秒
- [ ] 数据采集成功率≥99%
- [ ] 支持参数阈值设置和报警功能
- [ ] 历史数据查询响应时间<10秒
- [ ] 设备状态监控实时更新
- [ ] 数据质量监控和异常检测
- [ ] 支持设备远程配置和控制
- [ ] 数据存储压缩率≥50%
- [ ] 界面支持响应式设计，适配大屏显示
- [ ] 支持数据导出和报表生成
- [ ] 异常情况自动报警和通知
- [ ] 所有页面元素符合工业监控界面规范
- [ ] 支持7×24小时稳定运行

# 功能模块规格说明书：APS智能排程模块

- **模块ID**: MES-002
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 生产计划员, **I want to** 配置排程优化目标, **so that** 生成符合业务需求的最优生产计划。
- **As a** 生产计划员, **I want to** 设置生产约束条件, **so that** 确保排程结果的可执行性。
- **As a** 生产计划员, **I want to** 通过甘特图查看排程结果, **so that** 直观了解生产计划安排。
- **As a** 车间主管, **I want to** 手动调整排程计划, **so that** 应对临时变化和紧急插单。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 任务池中存在待排程的工序任务
- 设备产能和人员技能数据已维护
- 物料库存和可用性信息准确
- 排程算法引擎正常运行

### 核心流程

#### 2.1 排程参数配置流程
1. 进入APS排程配置页面
2. 设置优化目标权重：
   - 交期优先：最小化延期时间
   - 效率优先：最大化设备利用率
   - 成本优先：最小化生产成本
3. 配置约束条件：
   - 设备产能限制
   - 人员技能匹配
   - 物料可用性
   - 工作日历设置
4. 设置排程时间范围和粒度
5. 保存配置参数

#### 2.2 智能排程计算流程
1. 从任务池获取待排程任务
2. 加载设备、人员、物料等资源信息
3. 应用配置的优化目标和约束条件
4. 启动APS算法引擎进行计算
5. 生成多个可选排程方案
6. 评估方案的关键指标
7. 推荐最优方案并展示结果

#### 2.3 排程结果分析流程
1. 以甘特图形式展示排程结果
2. 显示关键性能指标：
   - 设备利用率
   - 交期达成率
   - 资源冲突情况
3. 识别瓶颈工序和关键路径
4. 提供优化建议和调整方案
5. 支持方案对比和选择

#### 2.4 排程调整优化流程
1. 识别排程中的问题和冲突
2. 手动拖拽调整任务时间
3. 实时验证调整的可行性
4. 自动重新计算影响的任务
5. 更新关键指标和预警信息
6. 确认调整并保存新方案

### 后置条件
- 生成可执行的生产排程计划
- 排程结果通过可行性验证
- 关键指标达到预期目标
- 排程方案准备下发执行

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：APS智能排程页面
### 页面目标：提供专业的智能排程配置和结果展示界面

### 信息架构：
- **顶部区域**：包含 排程配置面板, 计算控制按钮, 方案切换标签
- **中间区域**：包含 甘特图主视图, 资源利用率图表, 任务详情面板
- **底部区域**：包含 关键指标展示, 优化建议, 操作工具栏

### 交互逻辑与状态：

#### **排程配置面板**
- **优化目标设置：**
  - **交期优先：** 滑块控件，设置权重0-100%
  - **效率优先：** 滑块控件，设置权重0-100%
  - **成本优先：** 滑块控件，设置权重0-100%
  - **权重总和：** 自动计算，必须等于100%
- **约束条件配置：**
  - **设备产能：** 表格显示各设备的产能限制
  - **人员技能：** 矩阵显示人员与工序的匹配关系
  - **物料可用性：** 列表显示物料库存和到货计划
  - **工作日历：** 日历控件，设置工作日和班次

#### **计算控制按钮组**
- **开始排程按钮：**
  - **默认状态：** 绿色背景(#52C41A)，白色文字"开始排程"
  - **计算中状态：** 蓝色背景，显示"计算中..."和进度条
  - **完成状态：** 绿色背景，显示"重新排程"
- **停止计算按钮：** 红色边框按钮，中断排程计算
- **保存方案按钮：** 蓝色边框按钮，保存当前排程方案
- **导出计划按钮：** 灰色边框按钮，导出排程结果

#### **方案切换标签**
- **方案标签页：** 显示多个排程方案（方案A、方案B、方案C）
- **当前方案：** 蓝色高亮显示当前查看的方案
- **方案指标：** 显示各方案的关键指标对比
- **推荐标识：** 绿色星标标识推荐方案

#### **甘特图主视图**
- **时间轴：** 水平时间轴，支持缩放和滚动
- **资源轴：** 垂直资源轴，显示设备和人员
- **任务条：** 彩色条形图显示任务
  - **颜色编码：** 不同工序类型使用不同颜色
  - **任务信息：** 悬停显示任务详情
  - **拖拽调整：** 支持拖拽改变任务时间
- **依赖关系：** 箭头线显示任务间的依赖
- **关键路径：** 红色高亮显示关键路径
- **冲突标识：** 红色背景标识资源冲突

#### **甘特图交互功能**
- **缩放控制：** 时间轴支持小时/天/周级别缩放
- **滚动导航：** 支持水平和垂直滚动
- **任务拖拽：** 拖拽任务条调整开始时间
- **任务拉伸：** 拉伸任务条调整持续时间
- **右键菜单：** 右键任务显示操作菜单
- **选择高亮：** 点击任务高亮显示相关信息

#### **资源利用率图表**
- **设备利用率：** 柱状图显示各设备的利用率
- **人员利用率：** 柱状图显示各人员的工作负荷
- **时间分布：** 折线图显示利用率随时间的变化
- **瓶颈识别：** 红色标识利用率过高的资源

#### **任务详情面板**
- **任务基本信息：**
  - 任务编号：大号字体显示
  - 工序名称：突出显示
  - 产品信息：显示产品名称和规格
  - 加工数量：数值和单位
- **时间信息：**
  - 计划开始时间：日期时间显示
  - 计划结束时间：日期时间显示
  - 持续时间：小时或天数显示
  - 缓冲时间：显示时间缓冲
- **资源分配：**
  - 分配设备：显示设备名称和型号
  - 分配人员：显示操作员姓名和技能
  - 所需物料：列表显示物料需求

#### **关键指标展示**
- **指标卡片组：** 4个关键指标卡片
  - **设备利用率：** 百分比显示，目标值对比
  - **交期达成率：** 百分比显示，延期任务统计
  - **生产效率：** 数值显示，与历史对比
  - **资源冲突：** 数量显示，冲突详情链接
- **趋势图表：** 显示指标随时间的变化趋势

#### **优化建议面板**
- **瓶颈分析：** 识别生产瓶颈和改进建议
- **资源优化：** 资源配置优化建议
- **时间优化：** 时间安排优化建议
- **风险预警：** 潜在风险和应对措施

#### **排程调整工具**
- **时间调整：** 拖拽调整任务时间
- **资源调整：** 下拉选择重新分配资源
- **优先级调整：** 滑块调整任务优先级
- **批量调整：** 选择多个任务批量调整
- **撤销重做：** 支持操作的撤销和重做

#### **方案对比对话框**
- **方案列表：** 显示所有可选方案
- **指标对比表：** 表格对比各方案的关键指标
- **甘特图对比：** 并排显示不同方案的甘特图
- **选择方案：** 单选按钮选择最终方案

### 数据校验规则：

#### **优化目标权重**
- **校验规则：** 权重总和必须等于100%
- **错误提示文案：** "优化目标权重总和必须等于100%"

#### **约束条件**
- **校验规则：** 设备产能、人员技能等约束条件必须完整
- **错误提示文案：** "约束条件设置不完整，请检查必填项"

#### **排程时间范围**
- **校验规则：** 开始时间不能早于当前时间，结束时间必须晚于开始时间
- **错误提示文案：** "排程时间范围设置错误"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **排程配置**:
  - **优化目标权重 (optimization_weights)**: Object, 交期/效率/成本权重
  - **时间范围 (time_range)**: Object, 开始和结束时间
  - **约束条件 (constraints)**: Object, 设备/人员/物料约束
- **任务数据**:
  - **待排程任务 (pending_tasks)**: Array, 任务列表
  - **资源信息 (resources)**: Object, 设备和人员信息
  - **物料可用性 (material_availability)**: Object, 物料库存信息

### 展示数据
- **排程结果**: 甘特图数据、任务分配、时间安排
- **关键指标**: 设备利用率、交期达成率、生产效率
- **优化建议**: 瓶颈分析、改进建议、风险预警
- **方案对比**: 多方案的指标对比和可视化展示

### 空状态/零数据
- **无待排程任务**: 显示"暂无待排程任务，请先分解销售订单"
- **排程计算失败**: 显示"排程计算失败，请检查约束条件设置"
- **无可行方案**: 显示"当前约束条件下无可行方案，请调整配置"

### API接口
- **获取待排程任务**: GET /api/production/pending-tasks
- **执行排程计算**: POST /api/aps/schedule
- **保存排程方案**: POST /api/aps/schedule/save
- **获取排程结果**: GET /api/aps/schedule/{id}

## 5. 异常与边界处理 (Error & Edge Cases)

### **排程计算超时**
- **提示信息**: "排程计算超时，请简化约束条件或减少任务数量"
- **用户操作**: 提供停止计算按钮，建议优化配置

### **资源冲突无解**
- **提示信息**: "检测到无法解决的资源冲突，请调整约束条件"
- **用户操作**: 高亮冲突资源，提供调整建议

### **物料短缺影响**
- **提示信息**: "物料短缺可能影响排程结果，建议优先处理物料采购"
- **用户操作**: 显示短缺物料清单，提供采购建议

### **设备维护冲突**
- **提示信息**: "设备维护计划与生产任务冲突，请协调安排"
- **用户操作**: 显示冲突详情，提供调整选项

### **算法引擎异常**
- **提示信息**: "排程算法引擎异常，请联系系统管理员"
- **用户操作**: 提供错误日志查看，支持重启引擎

### **数据同步延迟**
- **提示信息**: "资源数据同步中，排程结果可能不是最新"
- **用户操作**: 显示同步状态，提供手动刷新功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多目标优化配置（交期、效率、成本）
- [ ] 约束条件设置完整（设备、人员、物料、时间）
- [ ] APS算法计算成功率≥95%
- [ ] 甘特图可视化展示清晰直观
- [ ] 支持手动拖拽调整排程计划
- [ ] 关键指标计算准确（利用率、达成率等）
- [ ] 瓶颈识别和优化建议准确
- [ ] 支持多方案生成和对比选择
- [ ] 排程结果可保存和导出
- [ ] 界面支持响应式设计，适配大屏显示
- [ ] 排程计算时间：100个任务<5分钟
- [ ] 甘特图交互响应时间<1秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘快捷键和无障碍访问

# 功能模块规格说明书：扫码报工模块

- **模块ID**: MES-005
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 车间操作员, **I want to** 扫描产品二维码快速报工, **so that** 提高报工效率和准确性。
- **As a** 车间操作员, **I want to** 批量扫码报工多个产品, **so that** 减少重复操作时间。
- **As a** 质量工程师, **I want to** 通过扫码关联质量数据, **so that** 建立完整的质量追溯链。
- **As a** 车间主管, **I want to** 实时查看报工进度, **so that** 掌握生产执行状态。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 产品已贴有唯一标识二维码
- 扫码设备正常工作
- 工位任务已开始执行
- 操作员已完成身份认证

### 核心流程

#### 2.1 产品扫码识别流程
1. 操作员使用扫码枪扫描产品二维码
2. 系统解析二维码获取产品UID
3. 验证产品UID的有效性和唯一性
4. 关联产品到当前工位任务
5. 显示产品基本信息和工艺要求
6. 确认产品状态和流转信息

#### 2.2 工序报工流程
1. 确认当前工序和操作内容
2. 选择报工类型（开工/完工/转序）
3. 录入关键工艺参数和质量数据
4. 拍照记录产品状态（可选）
5. 确认报工信息无误
6. 提交报工数据到系统

#### 2.3 批量报工流程
1. 启动批量报工模式
2. 连续扫描多个产品二维码
3. 系统自动累计产品数量
4. 统一设置工艺参数和质量状态
5. 批量提交所有产品的报工数据
6. 显示批量报工结果统计

#### 2.4 异常处理流程
1. 识别扫码异常或产品异常
2. 显示异常类型和处理建议
3. 操作员选择处理方式
4. 记录异常信息和处理结果
5. 通知相关人员处理异常
6. 更新产品和任务状态

### 后置条件
- 产品报工数据准确记录
- 生产进度实时更新
- 质量追溯链完整建立
- 异常情况得到处理

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：扫码报工界面
### 页面目标：提供快速便捷的扫码报工操作界面

### 信息架构：
- **顶部区域**：包含 扫码状态指示, 当前任务信息, 报工模式切换
- **中间区域**：包含 扫码结果显示, 产品信息面板, 参数录入区域
- **底部区域**：包含 报工操作按钮, 历史记录, 统计信息

### 交互逻辑与状态：

#### **扫码状态指示器**
- **待扫码状态：**
  - **指示灯：** 蓝色闪烁，提示"请扫描产品二维码"
  - **扫码框：** 虚线边框，显示扫码区域
  - **提示信息：** "将二维码对准扫描区域"
- **扫码成功状态：**
  - **指示灯：** 绿色常亮，提示"扫码成功"
  - **音效提示：** 播放成功提示音
  - **震动反馈：** 设备震动确认（移动设备）
- **扫码失败状态：**
  - **指示灯：** 红色闪烁，提示"扫码失败"
  - **错误信息：** 显示具体错误原因
  - **重试按钮：** 提供重新扫码选项

#### **报工模式切换**
- **单个报工模式：**
  - **模式标识：** 蓝色标签"单个报工"
  - **操作流程：** 扫码→录入→提交
  - **适用场景：** 精细化操作，需要详细记录
- **批量报工模式：**
  - **模式标识：** 绿色标签"批量报工"
  - **操作流程：** 连续扫码→统一录入→批量提交
  - **适用场景：** 大批量相同工序操作
- **快速报工模式：**
  - **模式标识：** 橙色标签"快速报工"
  - **操作流程：** 扫码→自动提交
  - **适用场景：** 简单工序，无需额外录入

#### **当前任务信息栏**
- **任务基本信息：**
  - **任务编号：** 大号字体显示
  - **工序名称：** 突出显示当前工序
  - **产品信息：** 显示产品名称和规格
  - **计划数量：** 显示计划加工数量
- **进度统计：**
  - **已报工：** 绿色数字显示已报工数量
  - **待报工：** 橙色数字显示待报工数量
  - **异常数：** 红色数字显示异常产品数
  - **完成率：** 百分比显示任务完成率

#### **扫码结果显示区**
- **产品UID显示：**
  - **UID编码：** 大号字体显示产品唯一标识
  - **扫码时间：** 显示扫码时间戳
  - **扫码次数：** 显示该产品的扫码次数
- **产品状态指示：**
  - **正常：** 绿色边框，"状态正常"
  - **异常：** 红色边框，"状态异常"
  - **重复：** 黄色边框，"重复扫码"
  - **错误：** 红色边框，"产品错误"

#### **产品信息面板**
- **基本信息展示：**
  - **产品名称：** 显示产品完整名称
  - **产品规格：** 显示规格型号
  - **生产批次：** 显示生产批次号
  - **客户订单：** 显示关联的客户订单
- **流转信息：**
  - **上一工序：** 显示上一工序名称和完成时间
  - **当前工序：** 高亮显示当前工序
  - **下一工序：** 显示下一工序名称
  - **流转状态：** 显示产品流转状态
- **质量信息：**
  - **质量状态：** 显示当前质量状态
  - **检验结果：** 显示最近检验结果
  - **质量等级：** 显示产品质量等级

#### **参数录入区域**
- **工艺参数录入：**
  - **参数列表：** 显示需要录入的工艺参数
  - **标准值：** 显示参数标准值或范围
  - **实测值：** 输入框录入实际测量值
  - **自动填充：** 支持从设备自动获取参数
- **质量数据录入：**
  - **检验项目：** 显示质量检验项目
  - **检验标准：** 显示检验标准要求
  - **检验结果：** 选择合格/不合格
  - **备注说明：** 文本框填写备注
- **快捷录入：**
  - **常用值：** 提供常用参数值快速选择
  - **历史值：** 显示历史录入值供参考
  - **模板应用：** 应用预设的参数模板

#### **报工操作按钮组**
- **开工报工按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"开工报工"
  - **点击效果：** 记录开工时间，更新产品状态
- **完工报工按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"完工报工"
  - **点击效果：** 记录完工时间，计算加工工时
- **转序报工按钮：**
  - **默认状态：** 橙色背景(#FAAD14)，"转序报工"
  - **点击效果：** 产品流转到下一工序
- **异常报工按钮：**
  - **默认状态：** 红色边框，"异常报工"
  - **点击效果：** 记录异常信息，暂停产品流转

#### **批量操作工具栏**
- **全选复选框：** 控制产品列表的全选/取消全选
- **批量开工：** 批量设置产品为开工状态
- **批量完工：** 批量设置产品为完工状态
- **批量转序：** 批量将产品转到下一工序
- **清空列表：** 清空当前扫码产品列表

#### **扫码历史记录**
- **记录列表：** 显示最近的扫码记录
- **记录内容：**
  - **产品UID：** 显示产品唯一标识
  - **扫码时间：** 显示扫码时间
  - **报工类型：** 显示报工操作类型
  - **操作员：** 显示操作员姓名
  - **状态：** 显示处理状态
- **筛选功能：** 按时间、状态、操作员筛选
- **导出功能：** 导出扫码记录到Excel

#### **统计信息面板**
- **今日统计：**
  - **扫码总数：** 显示今日扫码总数
  - **报工数量：** 显示今日报工数量
  - **异常数量：** 显示今日异常数量
  - **效率指标：** 显示报工效率
- **实时统计：**
  - **当前小时：** 显示当前小时的统计
  - **平均用时：** 显示平均报工用时
  - **成功率：** 显示扫码成功率

#### **异常处理对话框**
- **异常类型选择：**
  - **产品异常：** 产品质量或状态异常
  - **工艺异常：** 工艺参数异常
  - **设备异常：** 设备故障影响
  - **其他异常：** 其他类型异常
- **异常描述：**
  - **异常现象：** 详细描述异常现象
  - **发生时间：** 自动记录异常时间
  - **影响程度：** 选择异常影响程度
- **处理方式：**
  - **继续加工：** 异常不影响继续加工
  - **暂停处理：** 需要暂停处理异常
  - **报废处理：** 产品需要报废处理
  - **返工处理：** 产品需要返工处理

### 数据校验规则：

#### **产品UID**
- **校验规则：** UID必须符合系统编码规则，且在系统中存在
- **错误提示文案：** "产品二维码无效，请检查产品标识"

#### **工艺参数**
- **校验规则：** 参数值必须在标准范围内
- **错误提示文案：** "工艺参数超出标准范围，请重新测量"

#### **重复扫码**
- **校验规则：** 同一产品在同一工序不能重复报工
- **错误提示文案：** "该产品已完成当前工序报工"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **扫码数据**:
  - **产品UID (product_uid)**: String, 必填, 唯一标识
  - **扫码时间 (scan_time)**: DateTime, 自动记录
  - **扫码设备 (scan_device)**: String, 设备标识
  - **操作员ID (operator_id)**: String, 必填
- **报工数据**:
  - **报工类型 (report_type)**: Enum, [开工/完工/转序/异常]
  - **工艺参数 (process_params)**: Object, 工艺参数值
  - **质量数据 (quality_data)**: Object, 质量检验结果

### 展示数据
- **产品信息**: UID、名称、规格、批次、状态
- **扫码结果**: 扫码状态、时间、次数、历史
- **统计数据**: 扫码总数、报工数量、异常统计
- **进度信息**: 任务进度、完成率、效率指标

### 空状态/零数据
- **无扫码记录**: 显示"暂无扫码记录，请开始扫码报工"
- **产品信息缺失**: 显示"产品信息缺失，请联系管理员"
- **扫码设备异常**: 显示"扫码设备异常，请检查设备连接"

### API接口
- **产品UID验证**: POST /api/products/validate-uid
- **提交报工数据**: POST /api/production/report-work
- **获取扫码历史**: GET /api/production/scan-history
- **获取统计数据**: GET /api/production/scan-statistics

## 5. 异常与边界处理 (Error & Edge Cases)

### **二维码损坏无法识别**
- **提示信息**: "二维码损坏无法识别，请手动输入产品UID"
- **用户操作**: 提供手动输入选项，验证UID有效性

### **产品UID不存在**
- **提示信息**: "产品UID不存在，请检查产品标识"
- **用户操作**: 提供重新扫码选项，记录异常信息

### **产品状态异常**
- **提示信息**: "产品状态异常，无法进行当前工序报工"
- **用户操作**: 显示产品状态详情，提供异常处理流程

### **重复扫码报工**
- **提示信息**: "该产品已完成当前工序报工，是否查看详情？"
- **用户操作**: 显示历史报工记录，提供查看选项

### **扫码设备故障**
- **提示信息**: "扫码设备连接异常，请检查设备状态"
- **用户操作**: 提供设备检测功能，支持手动输入

### **网络连接中断**
- **提示信息**: "网络连接中断，数据将在恢复后自动同步"
- **用户操作**: 本地缓存扫码数据，网络恢复后同步

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持快速扫描产品二维码识别
- [ ] 产品UID验证准确率100%
- [ ] 支持单个、批量、快速三种报工模式
- [ ] 工艺参数和质量数据完整录入
- [ ] 异常产品识别和处理流程完整
- [ ] 扫码历史记录完整可查询
- [ ] 实时统计数据准确显示
- [ ] 支持离线扫码和数据同步
- [ ] 界面适配移动设备和工位终端
- [ ] 操作响应快速，用户体验良好
- [ ] 扫码识别时间<2秒
- [ ] 报工数据提交成功率≥99%
- [ ] 所有界面元素符合工业设计规范
- [ ] 支持语音提示和震动反馈

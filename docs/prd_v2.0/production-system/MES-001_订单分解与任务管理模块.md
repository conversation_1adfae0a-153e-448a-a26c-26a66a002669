# 功能模块规格说明书：订单分解与任务管理模块

- **模块ID**: MES-001
- **所属子系统**: 生产管理子系统(MES)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 生产计划员, **I want to** 自动同步ERP中的销售订单, **so that** 及时获取最新的生产需求。
- **As a** 生产计划员, **I want to** 将销售订单自动分解为工序任务, **so that** 为排程提供标准化的任务输入。
- **As a** 生产计划员, **I want to** 管理待排程任务池, **so that** 灵活调整任务优先级和状态。
- **As a** 车间主管, **I want to** 查看任务分解结果, **so that** 了解生产工作量和资源需求。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- ERP系统中存在已审核的销售订单
- 产品BOM和工艺路线已维护完整
- 用户具有生产计划管理权限
- 系统与ERP的数据接口正常

### 核心流程

#### 2.1 订单同步流程
1. 系统定时或手动触发ERP订单同步
2. 获取已审核且未分解的销售订单
3. 验证订单信息完整性（产品、数量、交期等）
4. 检查产品BOM和工艺路线的完整性
5. 将有效订单导入待分解订单列表
6. 异常订单记录错误信息并通知相关人员

#### 2.2 订单分解流程
1. 选择待分解的销售订单
2. 根据产品BOM获取物料清单
3. 根据工艺路线获取工序序列
4. 计算各工序的加工数量和时间
5. 建立工序间的前后置依赖关系
6. 生成工序任务并加入待排程任务池
7. 更新订单状态为"已分解"

#### 2.3 任务管理流程
1. 查看待排程任务池中的所有任务
2. 支持按订单、产品、工序等维度筛选
3. 调整任务优先级和紧急程度
4. 合并或拆分相似工序任务
5. 标记异常任务并处理
6. 为排程模块提供任务数据

### 后置条件
- 销售订单成功分解为工序任务
- 任务池数据准确完整
- 工序依赖关系正确建立
- 异常情况得到妥善处理

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：订单分解与任务管理页面
### 页面目标：提供高效的订单分解和任务管理界面

### 信息架构：
- **顶部区域**：包含 同步状态指示, 快捷操作按钮, 统计概览
- **中间区域**：包含 订单列表, 任务池列表, 分解详情面板
- **底部区域**：包含 分页控件, 批量操作, 导出功能

### 交互逻辑与状态：

#### **同步状态指示器**
- **正常状态：** 绿色指示灯，显示"ERP连接正常"
- **同步中状态：** 蓝色指示灯，显示"正在同步..."，带旋转动画
- **异常状态：** 红色指示灯，显示"连接异常"，闪烁提示
- **最后同步时间：** 显示相对时间，如"5分钟前"

#### **快捷操作按钮组**
- **手动同步按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，白色文字"同步订单"
  - **同步中状态：** 灰色背景，禁用状态，显示进度条
  - **悬停状态：** 背景色加深至#096DD9
- **批量分解按钮：** 绿色边框按钮，批量处理选中订单
- **任务优化按钮：** 橙色边框按钮，优化任务池结构
- **导出报告按钮：** 灰色边框按钮，导出分解报告

#### **统计概览卡片**
- **待分解订单：** 橙色数字，显示未分解订单数量
- **待排程任务：** 蓝色数字，显示任务池中的任务数
- **异常订单：** 红色数字，显示分解失败的订单
- **今日新增：** 绿色数字，显示今日新增的订单数

#### **订单列表表格**
- **表格样式：** 固定表头，斑马纹行，支持排序
- **列定义：**
  - 选择：复选框，支持批量选择
  - 订单编号：链接，点击查看订单详情
  - 客户名称：显示客户信息
  - 产品名称：显示产品规格
  - 订单数量：数值显示，单位标注
  - 交货日期：日期显示，逾期红色标识
  - 订单状态：状态标签
  - 同步时间：相对时间显示
  - 操作：分解、查看、重新同步

#### **订单状态标识**
- **待分解：** 橙色标签(#FAAD14)，"待分解"
- **分解中：** 蓝色标签(#1890FF)，"分解中"
- **已分解：** 绿色标签(#52C41A)，"已分解"
- **分解失败：** 红色标签(#F5222D)，"分解失败"
- **已排程：** 紫色标签(#722ED1)，"已排程"

#### **任务池列表**
- **任务卡片布局：** 卡片式展示，支持拖拽排序
- **任务信息显示：**
  - 任务编号：大号字体显示
  - 工序名称：突出显示工序类型
  - 产品信息：显示产品名称和规格
  - 加工数量：数值和单位
  - 预计工时：时间显示
  - 优先级：彩色标签标识
  - 依赖关系：显示前置任务

#### **任务优先级标识**
- **紧急：** 红色标签(#F5222D)，"紧急"，带闪烁效果
- **高优先级：** 橙色标签(#FAAD14)，"高"
- **普通：** 绿色标签(#52C41A)，"普通"
- **低优先级：** 灰色标签(#8C8C8C)，"低"

#### **分解详情面板**
- **订单基本信息：**
  - 订单编号：大号字体显示
  - 客户信息：客户名称和联系方式
  - 产品信息：产品名称、规格、图片
  - 数量和交期：突出显示关键信息
- **BOM结构树：**
  - 树形结构显示产品组成
  - 支持展开/收起节点
  - 显示物料编码、名称、数量
  - 缺料情况红色标识
- **工艺路线图：**
  - 流程图显示工序序列
  - 工序节点显示名称和工时
  - 连接线显示依赖关系
  - 关键路径高亮显示

#### **订单分解对话框**
- **分解选项设置：**
  - **分解模式：** 单选按钮（标准分解/快速分解/自定义分解）
  - **任务合并：** 复选框，是否合并相同工序
  - **优先级设置：** 下拉选择，设置任务优先级
- **分解进度显示：** 进度条显示分解进度
- **分解结果预览：** 表格显示将要生成的任务
- **确认分解按钮：** 绿色按钮，执行分解操作

#### **任务编辑对话框**
- **任务基本信息：** 只读显示任务编号、工序、产品
- **优先级调整：** 滑块控件，调整任务优先级
- **数量修改：** 数值输入框，修改加工数量
- **工时调整：** 时间输入框，调整预计工时
- **依赖关系：** 多选列表，设置前置任务
- **备注说明：** 文本区域，添加任务备注

#### **批量操作工具栏**
- **全选复选框：** 控制列表项的全选/取消全选
- **批量分解：** 绿色按钮，批量分解选中订单
- **批量删除：** 红色边框按钮，删除选中任务
- **优先级调整：** 下拉选择，批量调整优先级
- **导出任务：** 灰色边框按钮，导出任务清单

### 数据校验规则：

#### **订单信息**
- **校验规则：** 订单编号、产品、数量、交期不能为空
- **错误提示文案：** "订单信息不完整，请检查必填项"

#### **BOM完整性**
- **校验规则：** 产品必须有完整的BOM结构
- **错误提示文案：** "产品BOM不完整，无法进行分解"

#### **工艺路线**
- **校验规则：** 产品必须有有效的工艺路线
- **错误提示文案：** "产品工艺路线缺失，请先维护工艺信息"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **销售订单信息**:
  - **订单编号 (order_no)**: String, 必填, 唯一标识
  - **客户ID (customer_id)**: String, 必填
  - **产品编码 (product_code)**: String, 必填
  - **订单数量 (quantity)**: Number, 必填, 大于0
  - **交货日期 (delivery_date)**: Date, 必填
- **分解参数**:
  - **分解模式 (decompose_mode)**: Enum, [标准/快速/自定义]
  - **任务合并 (merge_tasks)**: Boolean, 默认true
  - **优先级 (priority)**: Enum, [低/普通/高/紧急]

### 展示数据
- **订单列表**: 订单基本信息、状态、同步时间
- **任务池**: 工序任务信息、优先级、依赖关系
- **分解详情**: BOM结构、工艺路线、分解结果
- **统计信息**: 订单数量、任务数量、异常统计

### 空状态/零数据
- **无订单数据**: 显示"暂无待分解订单，请先同步ERP数据"
- **任务池为空**: 显示"任务池为空，请先分解销售订单"
- **同步失败**: 显示"ERP连接异常，请检查网络连接"

### API接口
- **同步ERP订单**: GET /api/erp/orders/sync
- **分解订单**: POST /api/orders/{id}/decompose
- **获取任务池**: GET /api/production/tasks
- **更新任务**: PUT /api/production/tasks/{id}

## 5. 异常与边界处理 (Error & Edge Cases)

### **ERP连接异常**
- **提示信息**: "ERP系统连接异常，无法同步订单数据"
- **用户操作**: 显示重试按钮，提供手动刷新功能

### **BOM数据缺失**
- **提示信息**: "产品BOM数据不完整，无法进行订单分解"
- **用户操作**: 显示缺失的BOM项目，提供BOM维护入口

### **工艺路线异常**
- **提示信息**: "产品工艺路线缺失或异常，请先完善工艺信息"
- **用户操作**: 跳转到工艺管理模块，完善工艺路线

### **订单重复分解**
- **提示信息**: "订单已分解，是否重新分解？重新分解将清除原有任务"
- **用户操作**: 提供确认对话框，用户选择是否继续

### **任务依赖冲突**
- **提示信息**: "检测到任务依赖关系冲突，请检查工艺路线设置"
- **用户操作**: 高亮冲突的任务，提供依赖关系调整工具

### **数据同步延迟**
- **提示信息**: "数据同步中，请稍候..."
- **用户操作**: 显示同步进度，提供取消同步选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持自动和手动同步ERP销售订单
- [ ] 订单分解准确率100%，包含完整的工序信息
- [ ] 异常订单清晰提示并通知相关人员
- [ ] 分解操作具有幂等性，避免重复生成任务
- [ ] 支持任务优先级调整和批量操作
- [ ] 任务池数据实时更新，支持多维度筛选
- [ ] BOM和工艺路线完整性校验
- [ ] 工序依赖关系正确建立
- [ ] 界面支持响应式设计，适配不同屏幕
- [ ] 订单同步响应时间小于30秒
- [ ] 订单分解响应时间小于60秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

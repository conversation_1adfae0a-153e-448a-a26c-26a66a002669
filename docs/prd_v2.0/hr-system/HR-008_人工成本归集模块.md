# 功能模块规格说明书：人工成本归集模块

- **模块ID**: HR-008
- **所属子系统**: 人事管理子系统(HR)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 成本会计, **I want to** 自动归集人工成本, **so that** 准确核算产品成本和部门费用。
- **As a** 财务经理, **I want to** 分析人工成本结构, **so that** 优化成本控制和预算管理。
- **As a** 生产经理, **I want to** 查看车间人工成本, **so that** 了解生产成本构成和控制要点。
- **As a** 管理层, **I want to** 监控人工成本趋势, **so that** 制定人力资源和成本控制策略。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 薪酬数据已计算完成
- 组织架构和成本中心已配置
- 成本归集规则已设定
- 产品工艺路线已建立

### 核心流程

#### 2.1 人工成本归集流程
1. 获取员工薪酬和工时数据
2. 按成本中心和产品归集人工成本
3. 计算直接人工和间接人工成本
4. 分摊管理费用和辅助费用
5. 生成成本归集明细和汇总
6. 传递成本数据到财务系统
7. 生成成本分析报告

#### 2.2 成本分摊流程
1. 识别直接成本和间接成本
2. 确定成本分摊的基础和比例
3. 计算各产品的人工成本分摊
4. 处理跨部门和跨产品的成本
5. 调整异常和特殊情况的成本
6. 验证成本分摊的合理性
7. 确认成本分摊结果

#### 2.3 成本分析流程
1. 收集人工成本数据
2. 按多维度分析成本结构
3. 对比历史和预算成本
4. 识别成本异常和趋势
5. 分析成本驱动因素
6. 提出成本优化建议
7. 生成成本分析报告

### 后置条件
- 人工成本数据准确归集
- 成本分摊合理有据
- 财务系统数据同步
- 成本分析报告完整

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：人工成本归集页面
### 页面目标：提供人工成本的归集、分摊、分析和报告功能

### 信息架构：
- **顶部区域**：包含 归集周期, 成本中心, 产品筛选, 导出功能
- **左侧区域**：包含 成本分类, 归集规则, 分摊方式, 快速筛选
- **中间区域**：包含 成本明细, 归集结果, 分析图表
- **右侧区域**：包含 成本统计, 异常提醒, 操作面板

### 交互逻辑与状态：

#### **成本归集控制台**
- **归集任务管理：**
  - **任务设置：**
    - **归集周期：** 日期选择器，选择成本归集的周期
    - **归集范围：** 多选框，选择归集的部门或产品
    - **归集方式：** 单选按钮，按部门/按产品/按工序
    - **特殊参数：** 输入框，特殊归集参数设置
  - **任务执行：**
    - **开始归集：** 按钮，启动成本归集任务
    - **重新归集：** 按钮，重新执行归集任务
    - **暂停归集：** 按钮，暂停正在执行的归集
    - **取消归集：** 按钮，取消归集任务
  - **任务监控：**
    - **执行进度：** 进度条，显示归集任务的执行进度
    - **处理状态：** 状态标识，等待/执行中/完成/失败
    - **数据量：** 显示框，显示处理的数据量
    - **预计完成时间：** 显示框，预计任务完成时间

#### **成本归集规则配置**
- **直接人工成本：**
  - **归集规则：**
    - **计件工资：** 复选框，是否归集计件工资
    - **计时工资：** 复选框，是否归集计时工资
    - **加班工资：** 复选框，是否归集加班工资
    - **奖金津贴：** 复选框，是否归集奖金津贴
  - **归集方式：**
    - **按产量归集：** 单选按钮，按实际产量归集
    - **按工时归集：** 单选按钮，按实际工时归集
    - **按标准归集：** 单选按钮，按标准工时归集
- **间接人工成本：**
  - **管理人员工资：**
    - **车间管理：** 复选框，车间管理人员工资
    - **质检人员：** 复选框，质检人员工资
    - **设备维护：** 复选框，设备维护人员工资
    - **其他辅助：** 复选框，其他辅助人员工资
  - **分摊基础：**
    - **按产量分摊：** 单选按钮，按产量比例分摊
    - **按工时分摊：** 单选按钮，按工时比例分摊
    - **按人数分摊：** 单选按钮，按人数比例分摊
    - **按金额分摊：** 单选按钮，按金额比例分摊

#### **成本归集结果查看**
- **成本汇总信息：**
  - **总体统计：**
    - **总成本：** 显示框，人工成本总额
    - **直接成本：** 显示框，直接人工成本
    - **间接成本：** 显示框，间接人工成本
    - **成本占比：** 饼图，各类成本的占比
  - **部门成本：**
    - **部门列表：** 表格，各部门的人工成本
    - **成本对比：** 柱状图，部门间成本对比
    - **趋势分析：** 折线图，部门成本趋势
- **产品成本明细：**
  - **产品成本：**
    - **产品信息：** 显示产品名称、规格、数量
    - **直接人工：** 显示产品的直接人工成本
    - **间接人工：** 显示产品的间接人工成本
    - **单位成本：** 显示产品的单位人工成本
  - **工序成本：**
    - **工序明细：** 显示各工序的人工成本
    - **成本构成：** 显示工序成本的构成
    - **效率分析：** 分析工序的成本效率

#### **成本分摊管理**
- **分摊规则设置：**
  - **分摊对象：**
    - **产品分摊：** 复选框，按产品分摊成本
    - **部门分摊：** 复选框，按部门分摊成本
    - **工序分摊：** 复选框，按工序分摊成本
    - **项目分摊：** 复选框，按项目分摊成本
  - **分摊基础：**
    - **产量基础：** 数字输入框，按产量分摊的权重
    - **工时基础：** 数字输入框，按工时分摊的权重
    - **金额基础：** 数字输入框，按金额分摊的权重
    - **自定义基础：** 输入框，自定义分摊基础
- **分摊计算：**
  - **分摊计算：**
    - **自动计算：** 按钮，自动计算成本分摊
    - **手工调整：** 按钮，手工调整分摊结果
    - **分摊验证：** 按钮，验证分摊结果的正确性
    - **分摊确认：** 按钮，确认分摊结果

#### **成本分析功能**
- **成本结构分析：**
  - **成本构成：**
    - **工资构成：** 饼图显示工资的构成比例
    - **部门构成：** 柱状图显示各部门的成本构成
    - **产品构成：** 堆积图显示各产品的成本构成
  - **成本趋势：**
    - **月度趋势：** 折线图显示月度成本趋势
    - **同比分析：** 对比图显示同比成本变化
    - **环比分析：** 对比图显示环比成本变化
- **成本效率分析：**
  - **效率指标：**
    - **人均产值：** 计算人均产值指标
    - **人工成本率：** 计算人工成本占比
    - **单位成本：** 计算单位产品人工成本
    - **成本效率：** 计算成本效率指标
  - **对比分析：**
    - **历史对比：** 对比历史同期的成本效率
    - **预算对比：** 对比预算的成本效率
    - **标杆对比：** 对比行业标杆的成本效率

#### **成本数据传递**
- **数据传递设置：**
  - **传递目标：**
    - **财务系统：** 复选框，传递到财务系统
    - **成本系统：** 复选框，传递到成本系统
    - **ERP系统：** 复选框，传递到ERP系统
  - **传递格式：**
    - **数据格式：** 下拉选择，XML/JSON/CSV格式
    - **传递频率：** 下拉选择，实时/定时/手动
    - **传递规则：** 文本域，数据传递的规则
- **传递执行：**
  - **传递操作：**
    - **立即传递：** 按钮，立即传递成本数据
    - **定时传递：** 按钮，设置定时传递
    - **传递历史：** 链接，查看传递历史记录
    - **传递状态：** 显示框，显示传递状态

#### **异常处理功能**
- **异常识别：**
  - **数据异常：**
    - **成本异常：** 识别异常高或低的成本数据
    - **分摊异常：** 识别分摊比例的异常
    - **趋势异常：** 识别成本趋势的异常变化
  - **规则异常：**
    - **归集规则：** 识别归集规则的异常
    - **分摊规则：** 识别分摊规则的异常
    - **计算规则：** 识别计算规则的异常
- **异常处理：**
  - **处理方式：**
    - **自动修复：** 自动修复可修复的异常
    - **人工处理：** 提交人工处理的异常
    - **忽略处理：** 忽略不影响结果的异常
  - **处理记录：**
    - **异常日志：** 记录异常的详细日志
    - **处理过程：** 记录异常的处理过程
    - **处理结果：** 记录异常的处理结果

### 数据校验规则：

#### **成本金额**
- **校验规则：** 必须大于等于0，不能超过合理上限
- **错误提示文案：** "成本金额必须大于等于0且在合理范围内"

#### **分摊比例**
- **校验规则：** 分摊比例总和必须等于100%
- **错误提示文案：** "分摊比例总和必须等于100%"

#### **归集周期**
- **校验规则：** 必须是有效的日期范围，不能重复归集
- **错误提示文案：** "归集周期设置有误或该周期已归集"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **成本归集参数**:
  - **归集周期 (collection_period)**: String, 必填, YYYY-MM格式
  - **成本中心 (cost_center)**: String, 必填, 引用成本中心
  - **归集方式 (collection_method)**: String, 必填, 按部门/按产品
- **薪酬数据**:
  - **基本工资 (base_salary)**: Decimal, 必填, 单位元
  - **计件工资 (piece_salary)**: Decimal, 必填, 单位元
  - **津贴补贴 (allowances)**: Decimal, 必填, 单位元
- **工时数据**:
  - **实际工时 (actual_hours)**: Decimal, 必填, 单位小时
  - **标准工时 (standard_hours)**: Decimal, 必填, 单位小时

### 展示数据
- **成本汇总**: 各维度的人工成本汇总数据
- **成本明细**: 详细的成本归集和分摊明细
- **成本分析**: 成本结构和趋势分析数据
- **传递记录**: 成本数据的传递记录

### 空状态/零数据
- **无成本数据**: 显示"暂无人工成本数据，请先进行成本归集"
- **无分摊数据**: 显示"暂无成本分摊数据"
- **无分析数据**: 显示"数据不足，无法生成成本分析"

### API接口
- **成本归集**: POST /api/hr/cost-collection
- **成本分摊**: POST /api/hr/cost-allocation
- **成本查询**: GET /api/hr/labor-costs
- **成本分析**: GET /api/hr/cost-analysis
- **数据传递**: POST /api/hr/cost-transfer

## 5. 异常与边界处理 (Error & Edge Cases)

### **成本数据缺失**
- **提示信息**: "关键成本数据缺失，无法完成成本归集"
- **用户操作**: 显示缺失的具体数据和补充方法

### **分摊规则冲突**
- **提示信息**: "检测到成本分摊规则冲突，请检查规则配置"
- **用户操作**: 显示冲突的规则和解决建议

### **成本异常波动**
- **提示信息**: "检测到成本异常波动，请确认数据准确性"
- **用户操作**: 提供异常数据的详细信息和处理选项

### **数据传递失败**
- **提示信息**: "成本数据传递失败，请检查网络连接"
- **用户操作**: 提供重试选项和手动传递功能

### **计算性能问题**
- **提示信息**: "成本计算执行缓慢，请稍后重试"
- **用户操作**: 提供任务优化建议和分批处理选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持按部门、产品、工序等多维度归集人工成本
- [ ] 自动区分直接人工和间接人工成本
- [ ] 支持多种成本分摊方式和规则配置
- [ ] 与财务系统集成，自动传递成本数据
- [ ] 提供完整的成本分析和趋势分析功能
- [ ] 成本归集准确率≥99.5%，处理速度满足要求
- [ ] 异常成本自动识别和处理机制完善
- [ ] 支持成本数据的多格式导出和报表生成
- [ ] 所有页面元素符合全局设计规范
- [ ] 系统性能稳定，支持大规模成本数据处理

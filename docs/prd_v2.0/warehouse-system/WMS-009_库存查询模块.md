# 功能模块规格说明书：库存查询模块

- **模块ID**: WMS-009
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 销售人员, **I want to** 快速查询产品库存和可用性, **so that** 准确回复客户询价和交期。
- **As a** 采购人员, **I want to** 查看库存预警和安全库存, **so that** 及时制定采购计划。
- **As a** 仓管员, **I want to** 查询物料的具体库位分布, **so that** 快速定位和拣货。
- **As a** 财务人员, **I want to** 查询库存价值和周转分析, **so that** 进行财务分析和成本控制。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 库存数据实时同步
- 用户具有查询权限
- 查询条件有效
- 系统运行正常

### 核心流程

#### 2.1 实时库存查询流程
1. 输入查询条件（物料、仓库、库位等）
2. 系统实时查询最新库存数据
3. 展示库存数量、状态、位置信息
4. 提供库存明细和批次信息
5. 支持多维度筛选和排序
6. 导出查询结果和报表

#### 2.2 库存预警查询流程
1. 系统自动监控库存水位
2. 识别低于安全库存的物料
3. 生成库存预警清单
4. 按紧急程度分级显示
5. 提供补货建议和采购计划
6. 发送预警通知给相关人员

#### 2.3 库存分析查询流程
1. 选择分析维度和时间范围
2. 计算库存周转率和周转天数
3. 分析库存结构和ABC分类
4. 识别呆滞库存和积压物料
5. 生成库存分析报告
6. 提供优化建议和改进措施

#### 2.4 批次追踪查询流程
1. 输入批次号或物料编码
2. 查询批次的完整生命周期
3. 显示批次的库存分布
4. 追踪批次的出入库记录
5. 展示批次的质量状态
6. 提供批次关联信息

#### 2.5 库位利用率查询流程
1. 选择查询的仓库和区域
2. 计算库位的占用率和利用率
3. 分析库位的存储效率
4. 识别空闲和过载库位
5. 提供库位优化建议
6. 生成库位利用率报告

### 后置条件
- 查询结果准确展示
- 数据实时性保证
- 查询记录完整保存
- 相关报表可导出

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：库存查询页面
### 页面目标：提供全面、快速、准确的库存查询功能

### 信息架构：
- **顶部区域**：包含 查询条件, 快捷筛选, 查询类型
- **中间区域**：包含 查询结果, 数据展示, 详情面板
- **底部区域**：包含 分页控制, 导出功能, 统计信息

### 交互逻辑与状态：

#### **查询条件区域**
- **基础查询：**
  - **物料编码：** 输入框支持模糊搜索和扫码
  - **物料名称：** 输入框支持中文搜索
  - **仓库选择：** 下拉选择单个或多个仓库
  - **库区选择：** 级联选择库区和货架
  - **批次号：** 输入框精确查询批次
- **高级查询：**
  - **物料分类：** 树形选择物料分类
  - **供应商：** 下拉选择供应商
  - **库存状态：** 多选可用、冻结、预留等状态
  - **时间范围：** 日期选择器设置查询时间
  - **数量范围：** 设置库存数量的最小最大值
- **快捷查询：**
  - **低库存预警：** 一键查询低于安全库存的物料
  - **零库存：** 查询库存为零的物料
  - **呆滞库存：** 查询长期无变动的库存
  - **即将过期：** 查询即将过期的批次

#### **查询类型标签**
- **实时库存：** 蓝色标签，查询当前实时库存
- **库存预警：** 红色标签，查询库存预警信息
- **库存分析：** 绿色标签，查询库存分析数据
- **批次追踪：** 橙色标签，查询批次追踪信息
- **库位查询：** 紫色标签，查询库位利用情况

#### **查询结果展示**
- **列表视图：**
  - **物料信息：** 物料编码、名称、规格、图片
  - **库存数量：** 总库存、可用库存、预留库存
  - **库位分布：** 主要库位和分布数量
  - **批次信息：** 批次数量和最早批次日期
  - **库存状态：** 正常、预警、冻结等状态
  - **最后更新：** 库存最后更新时间
- **卡片视图：**
  - **物料卡片：** 大图标显示物料信息
  - **库存指标：** 突出显示关键库存指标
  - **状态标识：** 颜色标识库存状态
  - **快捷操作：** 卡片上的快捷操作按钮
- **表格视图：**
  - **详细数据：** 表格形式显示详细数据
  - **排序功能：** 支持多列排序
  - **筛选功能：** 列级筛选和搜索
  - **自定义列：** 用户自定义显示列

#### **库存状态指示**
- **库存充足：** 绿色圆点，库存高于安全库存
- **库存预警：** 黄色圆点，库存接近安全库存
- **库存不足：** 红色圆点，库存低于安全库存
- **零库存：** 灰色圆点，库存为零
- **库存冻结：** 蓝色圆点，库存被冻结
- **库存预留：** 橙色圆点，库存被预留

#### **详情面板**
- **基本信息：**
  - **物料详情：** 物料的完整信息
  - **库存总览：** 库存的总体情况
  - **成本信息：** 库存成本和价值
  - **供应商信息：** 主要供应商信息
- **库位分布：**
  - **分布图表：** 可视化显示库位分布
  - **库位列表：** 详细的库位清单
  - **占用率：** 各库位的占用情况
  - **容器信息：** 容器的使用情况
- **批次信息：**
  - **批次列表：** 所有批次的详细信息
  - **批次状态：** 各批次的状态和质量
  - **有效期：** 批次的有效期信息
  - **追溯信息：** 批次的追溯链条
- **历史记录：**
  - **出入库记录：** 最近的出入库记录
  - **库存变化：** 库存数量的变化趋势
  - **操作记录：** 相关的操作记录
  - **异常记录：** 异常情况的记录

#### **库存分析图表**
- **库存趋势图：**
  - **时间轴：** 显示时间范围选择
  - **趋势线：** 库存数量变化趋势
  - **关键节点：** 标记重要的变化节点
  - **预测线：** 基于历史数据的预测
- **库存结构图：**
  - **饼图：** 按分类显示库存结构
  - **柱状图：** 按仓库显示库存分布
  - **热力图：** 显示库位的热度分布
  - **ABC分析：** ABC分类的可视化
- **周转分析：**
  - **周转率：** 库存周转率指标
  - **周转天数：** 平均库存周转天数
  - **对比分析：** 与历史数据对比
  - **行业对比：** 与行业标准对比

#### **预警信息展示**
- **预警级别：**
  - **紧急预警：** 红色背景，立即处理
  - **重要预警：** 橙色背景，优先处理
  - **一般预警：** 黄色背景，关注处理
  - **提醒信息：** 蓝色背景，了解关注
- **预警内容：**
  - **库存不足：** 库存低于安全库存
  - **即将过期：** 批次即将过期
  - **呆滞库存：** 长期无变动库存
  - **异常波动：** 库存异常变化
- **处理建议：**
  - **补货建议：** 建议的补货数量和时间
  - **处理方案：** 针对预警的处理方案
  - **联系人：** 相关责任人联系方式
  - **截止时间：** 处理的截止时间

#### **操作按钮组**
- **查询按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，"查询"
  - **加载状态：** 显示加载动画，"查询中..."
  - **点击效果：** 执行查询操作
- **重置按钮：**
  - **默认状态：** 灰色边框，"重置"
  - **点击效果：** 清空所有查询条件
- **导出按钮：**
  - **默认状态：** 绿色边框，"导出"
  - **点击效果：** 导出查询结果
- **收藏按钮：**
  - **默认状态：** 橙色边框，"收藏查询"
  - **点击效果：** 收藏当前查询条件

#### **导出功能**
- **导出格式：**
  - **Excel格式：** 导出为Excel文件
  - **PDF格式：** 导出为PDF报告
  - **CSV格式：** 导出为CSV数据文件
  - **图片格式：** 导出图表为图片
- **导出内容：**
  - **当前页：** 导出当前页面数据
  - **全部数据：** 导出所有查询结果
  - **选中数据：** 导出选中的数据
  - **汇总报告：** 导出汇总分析报告
- **导出设置：**
  - **字段选择：** 选择要导出的字段
  - **格式设置：** 设置导出格式选项
  - **文件命名：** 自定义导出文件名
  - **邮件发送：** 直接邮件发送导出文件

#### **收藏查询管理**
- **收藏列表：**
  - **查询名称：** 显示收藏的查询名称
  - **查询条件：** 显示查询条件摘要
  - **创建时间：** 显示收藏创建时间
  - **使用频次：** 显示查询使用频次
- **快捷操作：**
  - **快速查询：** 一键执行收藏的查询
  - **编辑查询：** 编辑收藏的查询条件
  - **分享查询：** 分享查询给其他用户
  - **删除收藏：** 删除不需要的收藏

#### **实时刷新控制**
- **刷新设置：**
  - **自动刷新：** 开关控制自动刷新
  - **刷新间隔：** 设置自动刷新间隔
  - **手动刷新：** 手动刷新按钮
  - **最后更新：** 显示最后更新时间
- **数据状态：**
  - **实时数据：** 绿色指示，数据实时
  - **延迟数据：** 黄色指示，数据有延迟
  - **离线数据：** 红色指示，数据离线
  - **同步中：** 蓝色指示，数据同步中

#### **权限控制**
- **查询权限：**
  - **仓库权限：** 只能查询有权限的仓库
  - **物料权限：** 只能查询有权限的物料
  - **数据权限：** 控制可查看的数据范围
  - **功能权限：** 控制可使用的功能
- **数据脱敏：**
  - **成本信息：** 根据权限显示或隐藏成本
  - **敏感数据：** 对敏感数据进行脱敏
  - **详细信息：** 控制详细信息的访问
  - **导出权限：** 控制数据导出权限

### 数据校验规则：

#### **查询条件**
- **校验规则：** 至少输入一个有效的查询条件
- **错误提示文案：** "请输入至少一个查询条件"

#### **时间范围**
- **校验规则：** 结束时间不能早于开始时间
- **错误提示文案：** "结束时间不能早于开始时间"

#### **数量范围**
- **校验规则：** 最大值不能小于最小值
- **错误提示文案：** "最大值不能小于最小值"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **查询条件**:
  - **物料编码 (material_code)**: String, 可选, 支持模糊匹配
  - **仓库编码 (warehouse_code)**: String, 可选, 支持多选
  - **批次号 (batch_no)**: String, 可选, 精确匹配
  - **时间范围 (date_range)**: DateRange, 可选, 查询时间范围
- **筛选条件**:
  - **库存状态 (stock_status)**: Enum, 可选, 正常/预警/冻结
  - **数量范围 (quantity_range)**: NumberRange, 可选, 库存数量范围
  - **物料分类 (category)**: String, 可选, 物料分类

### 展示数据
- **库存信息**: 物料信息、库存数量、库位分布、批次详情
- **预警信息**: 预警级别、预警内容、处理建议、责任人
- **分析数据**: 周转率、库存结构、趋势分析、对比数据
- **统计信息**: 查询结果统计、汇总数据、关键指标

### 空状态/零数据
- **无查询结果**: 显示"未找到符合条件的库存数据"
- **无库存**: 显示"该物料当前无库存"
- **无预警**: 显示"当前无库存预警信息"

### API接口
- **库存查询**: GET /api/inventory/query
- **预警查询**: GET /api/inventory/alerts
- **库存分析**: GET /api/inventory/analysis
- **批次追踪**: GET /api/inventory/batch-trace

## 5. 异常与边界处理 (Error & Edge Cases)

### **查询超时**
- **提示信息**: "查询超时，请缩小查询范围或稍后重试"
- **用户操作**: 提供查询优化建议和重试选项

### **数据权限不足**
- **提示信息**: "您没有权限查询该仓库或物料的库存信息"
- **用户操作**: 显示权限范围，提供权限申请

### **查询结果过多**
- **提示信息**: "查询结果超过X条，建议缩小查询范围"
- **用户操作**: 提供筛选建议和分页显示

### **数据同步延迟**
- **提示信息**: "数据同步延迟，显示的可能不是最新数据"
- **用户操作**: 提供手动刷新和实时查询选项

### **网络连接异常**
- **提示信息**: "网络连接异常，无法获取最新库存数据"
- **用户操作**: 显示缓存数据，提供重新连接选项

### **系统维护**
- **提示信息**: "系统正在维护，库存查询功能暂时不可用"
- **用户操作**: 显示维护时间，提供离线查询选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多维度、多条件的库存查询
- [ ] 查询响应时间<3秒，大数据量<10秒
- [ ] 实时库存数据准确率≥99.9%
- [ ] 支持实时刷新和自动更新
- [ ] 库存预警及时准确，覆盖率≥95%
- [ ] 支持多种数据导出格式
- [ ] 查询结果支持多种展示方式
- [ ] 权限控制严格，数据安全可靠
- [ ] 界面响应式设计，支持移动端
- [ ] 查询历史记录和收藏功能完善
- [ ] 库存分析功能丰富，图表直观
- [ ] 所有页面元素符合全局设计规范
- [ ] 查询效率提升≥60%

# 功能模块规格说明书：质量检验模块

- **模块ID**: WMS-004
- **所属子系统**: 仓储管理子系统(WMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 质检员, **I want to** 扫码快速调取质检任务和标准, **so that** 高效完成质量检验工作。
- **As a** 质检员, **I want to** 在移动端录入检验结果, **so that** 实时更新质检状态和处理不合格品。
- **As a** 仓管员, **I want to** 根据质检结果自动分拣物料, **so that** 确保合格品正常入库，不合格品隔离处理。
- **As a** 质量主管, **I want to** 查看质检统计和趋势分析, **so that** 监控供应商质量和改进质量管理。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 质检标准和检验项目已配置
- 质检员具有质检权限
- 物料已完成收货待检状态
- 质检设备和工具准备就绪

### 核心流程

#### 2.1 质检任务创建流程
1. 收货入库时，系统根据物料类型自动创建质检任务
2. 或质检员手动扫码物料批次创建质检任务
3. 系统调取对应的质检标准和检验项目
4. 分配质检员并设置检验期限
5. 质检任务进入待检状态
6. 通知相关质检员执行检验

#### 2.2 质检执行流程
1. 质检员扫描批次条码调取质检任务
2. 系统显示检验项目和标准要求
3. 按检验项目逐项进行检测
4. 录入检验数据和结果判定
5. 上传检验照片或附件（如需要）
6. 系统自动计算综合质检结果
7. 确认并提交质检报告

#### 2.3 合格品处理流程
1. 质检结果为合格的物料
2. 系统自动更新物料状态为"合格"
3. 释放物料到正常库存
4. 可进行正常的出库和使用
5. 记录质检合格信息到批次档案

#### 2.4 不合格品处理流程
1. 质检结果为不合格的物料
2. 系统自动标记为"不合格"状态
3. 物料转移到不合格品隔离区
4. 生成不合格品处理单
5. 根据不合格类型选择处理方式
6. 执行退货、返工或报废处理
7. 更新供应商质量档案

#### 2.5 质检报告流程
1. 质检完成后自动生成质检报告
2. 质检员确认报告内容
3. 质量主管审核质检报告
4. 归档质检报告到质量档案
5. 必要时发送报告给相关部门

### 后置条件
- 质检结果准确记录
- 物料状态正确更新
- 不合格品得到妥善处理
- 质检数据完整归档

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：质量检验操作页面
### 页面目标：提供便捷高效的质量检验操作界面

### 信息架构：
- **顶部区域**：包含 质检任务信息, 扫码区域, 进度指示
- **中间区域**：包含 检验项目列表, 检验标准, 结果录入
- **底部区域**：包含 检验结果, 处理决定, 操作按钮

### 交互逻辑与状态：

#### **质检任务信息**
- **任务基本信息：**
  - **任务编号：** 显示质检任务唯一编号
  - **物料信息：** 显示物料编码、名称、规格
  - **批次信息：** 显示批次号、生产日期、数量
  - **供应商：** 显示供应商名称和编码
  - **创建时间：** 显示任务创建时间
  - **检验期限：** 显示要求完成检验的时间
- **任务状态指示：**
  - **待检：** 橙色圆点，"待检验"
  - **检验中：** 蓝色圆点，"检验中"
  - **已完成：** 绿色圆点，"检验完成"
  - **已逾期：** 红色圆点，"检验逾期"

#### **扫码区域**
- **扫码状态：**
  - **待扫码：** 蓝色虚线框，"请扫描批次条码"
  - **扫码成功：** 绿色实线框，显示扫码结果
  - **扫码失败：** 红色边框，"条码无效或不存在"
- **快捷操作：**
  - **重新扫码：** 重新扫描条码
  - **手动输入：** 手动输入批次号
  - **任务列表：** 查看所有待检任务

#### **检验项目列表**
- **项目分类：**
  - **外观检验：** 外观、包装、标识等
  - **尺寸检验：** 长度、宽度、厚度等
  - **性能检验：** 强度、硬度、透明度等
  - **化学检验：** 成分、纯度、有害物质等
- **检验项目卡片：**
  - **项目名称：** 显示检验项目名称
  - **检验标准：** 显示标准要求和允许偏差
  - **检验方法：** 显示检验方法和工具
  - **必检标识：** 红色星号标识必检项目
  - **检验状态：** 显示当前项目检验状态
- **项目状态：**
  - **未检：** 灰色背景，等待检验
  - **检验中：** 黄色背景，正在检验
  - **合格：** 绿色背景，检验合格
  - **不合格：** 红色背景，检验不合格
  - **免检：** 蓝色背景，免检项目

#### **检验标准显示**
- **标准信息：**
  - **标准名称：** 显示适用的检验标准
  - **标准编号：** 显示标准编号和版本
  - **技术要求：** 显示具体的技术要求
  - **检验方法：** 显示检验方法和步骤
  - **判定规则：** 显示合格/不合格判定规则
- **标准查看：**
  - **展开详情：** 点击展开查看详细标准
  - **标准文档：** 查看完整的标准文档
  - **历史版本：** 查看标准的历史版本

#### **结果录入区域**
- **数值型检验：**
  - **测量值输入：** 数字输入框录入测量值
  - **单位显示：** 显示测量单位
  - **标准范围：** 显示标准要求的数值范围
  - **偏差计算：** 自动计算与标准值的偏差
  - **判定结果：** 自动判定合格/不合格
- **定性检验：**
  - **选择判定：** 单选按钮选择合格/不合格
  - **等级评定：** 下拉选择质量等级
  - **缺陷记录：** 记录发现的缺陷类型
  - **备注说明：** 文本框输入检验备注
- **照片上传：**
  - **拍照按钮：** 调用摄像头拍照
  - **相册选择：** 从相册选择照片
  - **照片预览：** 预览上传的照片
  - **照片标注：** 在照片上添加标注说明

#### **检验结果汇总**
- **结果统计：**
  - **总检验项：** 显示总检验项目数
  - **已检项目：** 显示已完成检验的项目数
  - **合格项目：** 显示检验合格的项目数
  - **不合格项目：** 显示检验不合格的项目数
- **综合判定：**
  - **整体结果：** 显示整批物料的综合判定结果
  - **合格率：** 显示检验项目的合格率
  - **主要缺陷：** 显示主要的不合格项目
  - **质量等级：** 显示最终的质量等级

#### **处理决定区域**
- **合格品处理：**
  - **正常入库：** 合格品正常入库到指定库位
  - **优先使用：** 标记为优先使用
  - **特殊标识：** 添加特殊质量标识
- **不合格品处理：**
  - **隔离存放：** 转移到不合格品隔离区
  - **退货处理：** 退回供应商处理
  - **返工处理：** 安排返工修复
  - **报废处理：** 直接报废处理
  - **让步接收：** 在一定条件下让步接收
- **处理说明：**
  - **处理原因：** 选择或输入处理原因
  - **处理方式：** 详细说明处理方式
  - **责任方：** 确定责任归属
  - **后续跟踪：** 设置后续跟踪要求

#### **操作按钮组**
- **保存检验按钮：**
  - **默认状态：** 蓝色边框，"保存检验"
  - **点击效果：** 保存当前检验进度
- **提交报告按钮：**
  - **默认状态：** 绿色背景(#52C41A)，"提交报告"
  - **禁用状态：** 必检项目未完成时禁用
  - **点击效果：** 提交完整的质检报告
- **打印报告按钮：**
  - **默认状态：** 橙色边框，"打印报告"
  - **点击效果：** 打印质检报告
- **处理物料按钮：**
  - **默认状态：** 紫色边框，"处理物料"
  - **点击效果：** 执行物料处理决定

#### **质检报告预览**
- **报告头部：**
  - **报告编号：** 质检报告唯一编号
  - **检验日期：** 检验执行日期
  - **检验员：** 执行检验的质检员
  - **审核员：** 报告审核人员
- **检验详情：**
  - **物料信息：** 完整的物料和批次信息
  - **检验项目：** 所有检验项目和结果
  - **检验数据：** 详细的检验数据记录
  - **照片附件：** 检验过程中的照片
- **结论建议：**
  - **检验结论：** 最终的检验结论
  - **处理建议：** 对物料的处理建议
  - **改进建议：** 对供应商的改进建议

#### **质检历史记录**
- **记录列表：**
  - **检验时间：** 显示检验完成时间
  - **物料信息：** 显示检验的物料信息
  - **检验结果：** 显示检验结果
  - **质检员：** 显示执行检验的质检员
  - **处理结果：** 显示最终处理结果
- **筛选功能：**
  - **时间筛选：** 按检验时间筛选
  - **物料筛选：** 按物料类型筛选
  - **结果筛选：** 按检验结果筛选
  - **质检员筛选：** 按质检员筛选

### 数据校验规则：

#### **检验数据**
- **校验规则：** 数值必须在合理范围内，必检项目不能为空
- **错误提示文案：** "检验数据超出合理范围或必检项目未完成"

#### **处理决定**
- **校验规则：** 不合格品必须选择处理方式
- **错误提示文案：** "不合格品必须选择处理方式"

#### **报告完整性**
- **校验规则：** 所有必检项目必须完成检验
- **错误提示文案：** "存在未完成的必检项目，无法提交报告"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **质检任务**:
  - **任务编号 (task_no)**: String, 自动生成, 唯一标识
  - **批次号 (batch_no)**: String, 必填, 关联批次
  - **检验项目 (inspection_items)**: Array, 必填, 检验项目列表
  - **检验标准 (standards)**: Object, 必填, 检验标准信息
- **检验结果**:
  - **项目编码 (item_code)**: String, 必填, 检验项目标识
  - **检验值 (inspection_value)**: String/Number, 检验结果值
  - **判定结果 (result)**: Enum, 合格/不合格/免检
  - **备注说明 (remarks)**: String, 可选, 检验备注

### 展示数据
- **质检任务**: 任务信息、检验进度、检验状态
- **检验项目**: 项目列表、标准要求、检验结果
- **质检报告**: 检验结论、处理建议、统计数据
- **历史记录**: 检验历史、趋势分析、质量统计

### 空状态/零数据
- **无质检任务**: 显示"暂无待检任务"
- **无检验项目**: 显示"该物料无需质检"
- **无历史记录**: 显示"暂无质检记录"

### API接口
- **获取质检任务**: GET /api/quality/tasks
- **提交检验结果**: POST /api/quality/results
- **生成质检报告**: POST /api/quality/reports
- **处理不合格品**: POST /api/quality/nonconforming

## 5. 异常与边界处理 (Error & Edge Cases)

### **检验设备故障**
- **提示信息**: "检验设备异常，请检查设备状态或联系维护人员"
- **用户操作**: 提供设备检测功能，支持更换设备

### **检验标准缺失**
- **提示信息**: "该物料缺少检验标准，请联系质量部门"
- **用户操作**: 提供标准查询功能，支持临时标准录入

### **检验数据异常**
- **提示信息**: "检验数据超出正常范围，请重新检验"
- **用户操作**: 提供数据验证，支持重新检验

### **不合格品处理延迟**
- **提示信息**: "不合格品处理超时，请及时处理"
- **用户操作**: 发送提醒通知，升级处理流程

### **质检员权限不足**
- **提示信息**: "您没有权限执行此质检任务"
- **用户操作**: 显示权限要求，提供权限申请

### **网络中断影响**
- **提示信息**: "网络连接中断，检验数据将在恢复后同步"
- **用户操作**: 本地缓存数据，网络恢复后同步

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持多种检验项目类型（数值型、定性、等级评定）
- [ ] 检验标准和方法可配置，支持多版本管理
- [ ] 扫码调取质检任务响应时间<2秒
- [ ] 检验结果自动判定准确率≥99%
- [ ] 不合格品处理流程完整，状态跟踪准确
- [ ] 质检报告自动生成，格式规范完整
- [ ] 支持检验照片上传和标注功能
- [ ] 界面适配移动端，支持PDA操作
- [ ] 质检数据实时同步，离线操作支持
- [ ] 质检统计和分析功能完善
- [ ] 供应商质量档案自动更新
- [ ] 所有页面元素符合全局设计规范
- [ ] 质检效率提升≥50%，质量控制准确率≥99.5%

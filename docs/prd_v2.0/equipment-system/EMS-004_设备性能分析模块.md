# 功能模块规格说明书：设备性能分析模块

- **模块ID**: EMS-004
- **所属子系统**: 设备管理子系统(Equipment Management System)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 设备工程师, **I want to** 分析设备运行性能和效率, **so that** 识别设备问题和优化机会。
- **As a** 生产经理, **I want to** 监控设备OEE指标, **so that** 提升生产效率和设备利用率。
- **As a** 维护主管, **I want to** 分析设备故障模式和趋势, **so that** 制定预防性维护策略。
- **As a** 管理层, **I want to** 查看设备健康状态报告, **so that** 做出设备投资和更新决策。
- **As a** 质量工程师, **I want to** 分析设备性能对产品质量的影响, **so that** 优化工艺参数和质量控制。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备档案已在EMS-001中建立
- 设备运行数据已通过MES-006采集
- 维护记录已在EMS-002中记录
- 用户具有性能分析权限

### 核心流程

#### 2.1 设备性能数据采集流程
1. 从MES系统获取设备运行数据
2. 从维护系统获取维护记录
3. 从生产系统获取产量数据
4. 数据清洗和标准化处理
5. 计算基础性能指标
6. 存储到性能分析数据库
7. 触发异常数据预警
8. 更新实时性能仪表板
9. 生成性能分析报告

#### 2.2 OEE分析计算流程
1. 获取设备计划运行时间
2. 计算设备实际运行时间
3. 统计设备停机时间和原因
4. 计算设备可用率(Availability)
5. 统计产品合格率数据
6. 计算设备质量率(Quality)
7. 分析设备运行速度效率
8. 计算设备效率率(Performance)
9. 综合计算OEE指标

#### 2.3 故障分析预测流程
1. 收集设备历史故障数据
2. 分析故障模式和频率
3. 识别故障关键影响因素
4. 建立故障预测模型
5. 监控设备异常指标
6. 预测潜在故障风险
7. 生成预防性维护建议
8. 发送故障预警通知
9. 跟踪预测准确性

### 后置条件
- 设备性能指标实时更新
- OEE分析报告准确生成
- 故障预警及时发送
- 性能优化建议可行

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备性能分析页面
### 页面目标：提供设备性能监控、OEE分析和故障预测功能

### 信息架构：
- **顶部区域**：包含 时间筛选, 设备筛选, 分析类型, 报表导出
- **左侧区域**：包含 设备分类, 性能指标, 分析维度, 对比选项
- **中间区域**：包含 性能仪表板, 趋势图表, 分析报告
- **右侧区域**：包含 关键指标, 预警信息, 改进建议

### 交互逻辑与状态：

#### **性能分析筛选区域**
- **时间筛选：**
  - **分析周期：** 单选按钮，实时/小时/日/周/月
  - **时间范围：** 日期范围选择器
  - **班次筛选：** 复选框，早班/中班/晚班
  - **工作日筛选：** 复选框，工作日/周末/节假日
- **设备筛选：**
  - **设备分类：** 树形选择器，支持多选
  - **设备名称：** 搜索选择器，支持多选
  - **设备状态：** 复选框，运行/停机/维修
  - **生产线：** 下拉选择生产线
- **分析维度：**
  - **分析类型：** 单选按钮，OEE分析/故障分析/效率分析
  - **对比维度：** 下拉选择，同期对比/环比/设备对比
  - **聚合方式：** 单选按钮，平均值/最大值/最小值

#### **OEE性能仪表板**
- **核心指标：**
  - **OEE总体：** 仪表盘显示，目标值和实际值
  - **可用率：** 仪表盘显示，百分比格式
  - **效率率：** 仪表盘显示，百分比格式
  - **质量率：** 仪表盘显示，百分比格式
- **趋势图表：**
  - **OEE趋势：** 折线图，显示OEE变化趋势
  - **三率对比：** 柱状图，对比可用率、效率率、质量率
  - **损失分析：** 饼图，显示各类损失占比
  - **设备对比：** 雷达图，多设备OEE对比
- **详细数据：**
  - **计划时间：** 显示设备计划运行时间
  - **运行时间：** 显示设备实际运行时间
  - **停机时间：** 显示设备停机时间
  - **故障时间：** 显示设备故障时间
  - **产量数据：** 显示实际产量和计划产量
  - **质量数据：** 显示合格品数量和不合格品数量

#### **设备效率分析界面**
- **效率指标：**
  - **设备利用率：** 百分比显示，运行时间/计划时间
  - **生产效率：** 百分比显示，实际产量/理论产量
  - **能耗效率：** 显示单位产品能耗
  - **人员效率：** 显示人均产出效率
- **效率趋势：**
  - **日效率趋势：** 折线图显示日效率变化
  - **月效率对比：** 柱状图显示月度效率对比
  - **班次效率：** 柱状图显示各班次效率
  - **设备效率排名：** 表格显示设备效率排名
- **影响因素：**
  - **停机原因：** 饼图显示停机原因分布
  - **速度损失：** 柱状图显示速度损失原因
  - **质量损失：** 饼图显示质量损失原因
  - **改进机会：** 列表显示效率改进机会

#### **故障分析预测界面**
- **故障统计：**
  - **故障次数：** 显示统计周期内故障总次数
  - **故障时长：** 显示故障总时长和平均时长
  - **MTBF：** 显示平均故障间隔时间
  - **MTTR：** 显示平均故障修复时间
- **故障分析：**
  - **故障类型：** 饼图显示故障类型分布
  - **故障部件：** 柱状图显示故障部件统计
  - **故障趋势：** 折线图显示故障趋势变化
  - **故障影响：** 表格显示故障对生产的影响
- **预测预警：**
  - **健康指数：** 仪表盘显示设备健康状态
  - **故障风险：** 显示未来故障风险等级
  - **预警信息：** 列表显示设备预警信息
  - **维护建议：** 显示预防性维护建议
- **故障模式：**
  - **帕累托图：** 显示故障模式重要性排序
  - **鱼骨图：** 显示故障原因分析
  - **故障树：** 显示故障逻辑关系
  - **改进措施：** 列表显示故障改进措施

#### **性能对比分析**
- **设备对比：**
  - **对比设备：** 多选框选择对比设备
  - **对比指标：** 复选框选择对比指标
  - **对比图表：** 雷达图显示多维度对比
  - **对比表格：** 表格显示详细对比数据
- **时间对比：**
  - **对比周期：** 下拉选择对比时间周期
  - **同期对比：** 显示同期数据对比
  - **环比对比：** 显示环比数据变化
  - **趋势分析：** 折线图显示变化趋势
- **基准对比：**
  - **行业基准：** 显示行业平均水平
  - **内部基准：** 显示企业内部最佳实践
  - **目标对比：** 显示与目标值的差距
  - **改进空间：** 计算和显示改进潜力

#### **性能报告生成**
- **报告配置：**
  - **报告类型：** 单选按钮，日报/周报/月报/年报
  - **报告内容：** 复选框选择报告章节
  - **数据范围：** 设置报告数据范围
  - **输出格式：** 单选按钮，PDF/Excel/Word
- **报告预览：**
  - **报告标题：** 输入框设置报告标题
  - **报告摘要：** 文本域编辑报告摘要
  - **图表选择：** 复选框选择包含的图表
  - **数据表格：** 复选框选择包含的数据表
- **报告分发：**
  - **接收人：** 多选框选择报告接收人
  - **发送方式：** 复选框，邮件/系统通知
  - **发送时间：** 日期时间选择器
  - **定期发送：** 复选框设置定期发送

### 数据校验规则：

#### **时间范围**
- **校验规则：** 结束时间必须晚于开始时间，时间范围不能超过1年
- **错误提示文案：** "时间范围设置不合理，请重新选择"

#### **设备选择**
- **校验规则：** 至少选择一台设备进行分析
- **错误提示文案：** "请至少选择一台设备进行分析"

#### **指标阈值**
- **校验规则：** 预警阈值必须在合理范围内，上限大于下限
- **错误提示文案：** "预警阈值设置不合理，请检查设置"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **运行数据**:
  - **设备ID (equipment_id)**: String, 必填, 引用设备档案
  - **数据时间 (data_time)**: DateTime, 必填, 数据采集时间
  - **运行状态 (status)**: String, 必填, 运行/停机/故障
  - **产量数据 (output)**: Integer, 可选, 产量数量
- **故障数据**:
  - **故障时间 (fault_time)**: DateTime, 必填, 故障发生时间
  - **故障类型 (fault_type)**: String, 必填, 故障分类
  - **故障部件 (fault_component)**: String, 必填, 故障部件
  - **修复时间 (repair_time)**: DateTime, 必填, 故障修复时间

### 展示数据
- **OEE指标**: 可用率、效率率、质量率、综合OEE
- **性能趋势**: 各项指标的时间趋势图表
- **故障分析**: 故障统计、故障模式、预测预警
- **对比分析**: 设备对比、时间对比、基准对比

### 空状态/零数据
- **无运行数据**: 显示"暂无设备运行数据"
- **无故障记录**: 显示"该设备暂无故障记录"
- **无对比数据**: 显示"暂无可对比的数据"

### API接口
- **性能数据**: GET /api/equipment/performance
- **OEE计算**: GET /api/equipment/oee
- **故障分析**: GET /api/equipment/faults
- **预测分析**: GET /api/equipment/prediction
- **报告生成**: POST /api/equipment/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据缺失**
- **提示信息**: "设备运行数据不完整，分析结果可能不准确"
- **用户操作**: 提供数据补充建议和替代分析方法

### **计算异常**
- **提示信息**: "OEE计算出现异常，请检查基础数据"
- **用户操作**: 提供数据检查工具和手动修正选项

### **预测失效**
- **提示信息**: "故障预测模型需要更新，预测结果仅供参考"
- **用户操作**: 提供模型重训练和专家判断选项

### **性能异常**
- **提示信息**: "检测到设备性能异常，建议立即检查"
- **用户操作**: 提供异常详情和处理建议

### **报告生成失败**
- **提示信息**: "报告生成失败，请检查数据完整性"
- **用户操作**: 提供重试选项和简化报告模板

## 6. 验收标准 (Acceptance Criteria)

- [ ] OEE计算准确，三率分解清晰
- [ ] 设备性能趋势分析功能完整
- [ ] 故障分析和预测功能有效
- [ ] 性能对比分析功能准确
- [ ] 实时性能监控仪表板响应及时
- [ ] 与设备档案系统(EMS-001)集成正常
- [ ] 与维护管理系统(EMS-002)集成正常
- [ ] 与MES数据采集系统集成正常
- [ ] 性能报告生成功能完善
- [ ] 预警功能及时有效
- [ ] 数据可视化图表清晰美观
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<5秒，支持大数据量分析
- [ ] 分析结果准确可靠，支持决策制定

# 功能模块规格说明书：设备档案管理模块

- **模块ID**: EMS-001
- **所属子系统**: 设备管理子系统(Equipment Management System)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 设备管理员, **I want to** 建立和维护设备档案信息, **so that** 为设备全生命周期管理提供基础数据。
- **As a** 设备管理员, **I want to** 管理设备分类和编码规则, **so that** 确保设备信息的标准化和规范性。
- **As a** 生产经理, **I want to** 查看设备的技术参数和状态信息, **so that** 合理安排生产计划和设备使用。
- **As a** 维护工程师, **I want to** 获取设备的详细技术资料, **so that** 高效执行设备维护和故障排除。
- **As a** 采购专员, **I want to** 查看设备供应商信息和采购历史, **so that** 为设备采购决策提供参考。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有设备管理权限
- 供应商主数据已在系统中建立
- 设备分类体系已定义
- 设备编码规则已配置

### 核心流程

#### 2.1 设备档案创建流程
1. 选择设备分类和设备类型
2. 系统自动生成设备编码或手工输入
3. 填写设备基本信息（名称、型号、规格等）
4. 录入技术参数和性能指标
5. 设置设备供应商和采购信息
6. 上传设备相关文档和图片
7. 设置设备初始状态和位置信息
8. 提交设备档案审核
9. 审核通过后设备档案生效

#### 2.2 设备信息维护流程
1. 查询需要更新的设备档案
2. 修改设备基本信息或技术参数
3. 更新设备状态和位置信息
4. 记录变更原因和变更说明
5. 提交变更审批（重要信息变更）
6. 审批通过后更新设备档案
7. 系统记录变更历史
8. 发送变更通知给相关人员

#### 2.3 设备状态管理流程
1. 根据设备实际情况更新状态
2. 记录状态变更时间和原因
3. 触发相应的业务流程（如停机维护）
4. 更新设备可用性和生产计划
5. 发送状态变更通知
6. 记录状态变更日志

### 后置条件
- 设备档案信息完整准确
- 设备编码唯一且符合规范
- 设备状态实时更新
- 变更历史完整记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备档案管理页面
### 页面目标：提供设备档案的创建、维护和查询功能

### 信息架构：
- **顶部区域**：包含 设备搜索, 新建设备, 批量操作, 导入导出
- **左侧区域**：包含 设备分类树, 状态筛选, 位置筛选
- **中间区域**：包含 设备列表, 设备详情, 编辑界面
- **右侧区域**：包含 设备统计, 快速操作, 相关信息

### 交互逻辑与状态：

#### **设备查询区域**
- **基础查询：**
  - **设备名称：** 输入框，支持模糊搜索
  - **设备编码：** 输入框，精确查询
  - **设备型号：** 输入框，支持模糊搜索
  - **设备状态：** 多选下拉，运行中/停机/维修中/报废
- **高级查询：**
  - **设备分类：** 树形选择器，支持多选
  - **采购时间：** 日期范围选择器
  - **供应商：** 下拉选择器，支持搜索
  - **使用部门：** 下拉选择使用部门

#### **设备列表区域**
- **列表表头：**
  - **设备编码：** 可排序，点击查看详情
  - **设备名称：** 显示设备名称和型号
  - **设备分类：** 显示分类路径
  - **设备状态：** 状态标签
  - **使用部门：** 显示所属部门
  - **采购日期：** 显示采购时间
  - **供应商：** 显示供应商名称
  - **操作：** 编辑、查看、维护等操作
- **状态标识：**
  - **运行中：** 绿色标签，"运行中"
  - **停机：** 橙色标签，"停机"
  - **维修中：** 红色标签，"维修中"
  - **报废：** 灰色标签，"报废"

#### **设备档案创建/编辑界面**
- **基本信息：**
  - **设备编码：** 输入框，自动生成或手工输入，必填
  - **设备名称：** 输入框，必填，最大100字符
  - **设备型号：** 输入框，必填，最大50字符
  - **设备分类：** 树形选择器，必填
- **技术参数：**
  - **额定功率：** 数字输入框，单位kW
  - **工作电压：** 数字输入框，单位V
  - **设备尺寸：** 输入框，长×宽×高格式
  - **设备重量：** 数字输入框，单位kg
- **采购信息：**
  - **供应商：** 下拉搜索选择器，必填
  - **采购日期：** 日期选择器，必填
  - **采购价格：** 数字输入框，支持千分位
  - **质保期限：** 数字输入框，单位月
- **使用信息：**
  - **使用部门：** 下拉选择器，必填
  - **安装位置：** 输入框，详细位置描述
  - **责任人：** 搜索选择器，选择设备负责人
  - **投用日期：** 日期选择器

#### **设备技术资料管理**
- **文档管理：**
  - **技术手册：** 文件上传，支持PDF格式
  - **操作说明：** 文件上传，支持多种格式
  - **电路图纸：** 文件上传，支持图片和CAD格式
  - **维修手册：** 文件上传，支持PDF格式
- **图片管理：**
  - **设备照片：** 图片上传，支持多张图片
  - **铭牌照片：** 图片上传，设备铭牌信息
  - **安装现场：** 图片上传，安装位置照片
- **文档版本：**
  - **版本号：** 显示文档版本信息
  - **更新时间：** 显示最后更新时间
  - **更新人：** 显示文档更新人员

#### **设备状态管理**
- **状态信息：**
  - **当前状态：** 显示设备当前运行状态
  - **状态时间：** 显示状态持续时间
  - **下次维护：** 显示下次计划维护时间
  - **运行时长：** 显示累计运行时间
- **状态变更：**
  - **目标状态：** 下拉选择要变更的状态
  - **变更原因：** 下拉选择预设原因
  - **变更说明：** 文本域，详细说明
  - **生效时间：** 日期时间选择器
- **状态历史：**
  - **变更时间：** 显示状态变更时间
  - **变更人：** 显示操作人员
  - **原状态：** 显示变更前状态
  - **新状态：** 显示变更后状态
  - **变更原因：** 显示变更原因

#### **设备统计信息**
- **基础统计：**
  - **设备总数：** 显示系统中设备总数
  - **运行设备：** 显示正常运行的设备数
  - **停机设备：** 显示停机的设备数
  - **维修设备：** 显示正在维修的设备数
- **分类统计：**
  - **饼图：** 显示各分类设备分布
  - **柱状图：** 显示各部门设备数量
  - **列表：** 显示各状态设备明细
- **价值统计：**
  - **设备总价值：** 显示所有设备采购总价值
  - **年度采购：** 显示当年设备采购金额
  - **平均价值：** 计算设备平均价值
  - **折旧情况：** 显示设备折旧统计

### 数据校验规则：

#### **设备编码**
- **校验规则：** 必须唯一，符合编码规则格式
- **错误提示文案：** "设备编码已存在，请重新输入"

#### **设备名称**
- **校验规则：** 必填，长度1-100字符，不能包含特殊字符
- **错误提示文案：** "设备名称为必填项，长度不能超过100字符"

#### **技术参数**
- **校验规则：** 数值类型参数必须大于0，格式必须正确
- **错误提示文案：** "请输入正确的技术参数数值"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **设备基本信息**:
  - **设备编码 (equipment_code)**: String, 必填, 全局唯一
  - **设备名称 (equipment_name)**: String, 必填, 最大100字符
  - **设备型号 (model)**: String, 必填, 最大50字符
  - **设备分类ID (category_id)**: String, 必填, 引用设备分类
- **技术参数**:
  - **额定功率 (rated_power)**: Decimal, 可选, 单位kW
  - **工作电压 (voltage)**: Decimal, 可选, 单位V
  - **设备尺寸 (dimensions)**: String, 可选, 长×宽×高格式
- **采购信息**:
  - **供应商ID (supplier_id)**: String, 必填, 引用供应商主数据
  - **采购日期 (purchase_date)**: Date, 必填
  - **采购价格 (purchase_price)**: Decimal, 必填, 大于0

### 展示数据
- **设备列表**: 设备编码、名称、分类、状态、部门、供应商
- **设备详情**: 完整的设备信息和技术参数
- **统计信息**: 设备数量、状态分布、价值统计
- **变更历史**: 设备信息变更和状态变更记录

### 空状态/零数据
- **无设备数据**: 显示"暂无设备档案，点击新建设备开始"
- **无技术资料**: 显示"暂无技术资料"
- **无变更记录**: 显示"暂无变更记录"

### API接口
- **设备查询**: GET /api/equipment
- **设备创建**: POST /api/equipment
- **设备更新**: PUT /api/equipment/{id}
- **设备删除**: DELETE /api/equipment/{id}
- **状态变更**: POST /api/equipment/{id}/status
- **文档上传**: POST /api/equipment/{id}/documents

## 5. 异常与边界处理 (Error & Edge Cases)

### **设备编码重复**
- **提示信息**: "设备编码已存在，请使用其他编码"
- **用户操作**: 提供编码建议或自动生成选项

### **供应商信息缺失**
- **提示信息**: "供应商信息不存在，请先创建供应商档案"
- **用户操作**: 提供跳转到供应商管理的快捷链接

### **文件上传失败**
- **提示信息**: "文件上传失败，请检查文件格式和大小"
- **用户操作**: 提供重试按钮和格式说明

### **权限不足**
- **提示信息**: "您没有权限创建/修改设备档案"
- **用户操作**: 显示所需权限和申请流程

### **数据保存失败**
- **提示信息**: "数据保存失败，请检查网络连接后重试"
- **用户操作**: 提供重试按钮和数据恢复选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 设备档案创建功能完整，信息录入便捷
- [ ] 设备编码唯一性校验有效
- [ ] 设备分类管理和编码规则正确
- [ ] 设备状态管理流程清晰
- [ ] 设备查询和筛选功能准确
- [ ] 技术资料管理功能完善
- [ ] 数据校验规则完善，错误提示友好
- [ ] 设备统计信息实时准确
- [ ] 支持批量操作和数据导入导出
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 操作日志完整，支持审计追踪
- [ ] 与其他子系统集成正常

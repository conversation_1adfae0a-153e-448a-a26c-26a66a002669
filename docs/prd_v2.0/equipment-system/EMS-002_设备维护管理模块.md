# 功能模块规格说明书：设备维护管理模块

- **模块ID**: EMS-002
- **所属子系统**: 设备管理子系统(Equipment Management System)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 维护工程师, **I want to** 制定和执行设备维护计划, **so that** 确保设备正常运行和延长使用寿命。
- **As a** 设备管理员, **I want to** 跟踪设备维护历史和成本, **so that** 优化维护策略和控制维护成本。
- **As a** 生产经理, **I want to** 查看设备维护计划和状态, **so that** 合理安排生产计划避免冲突。
- **As a** 维护主管, **I want to** 分配维护任务和监控执行进度, **so that** 确保维护工作按时完成。
- **As a** 财务人员, **I want to** 统计设备维护成本, **so that** 进行成本分析和预算控制。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备档案已在EMS-001中建立
- 维护人员信息已录入系统
- 维护计划模板已配置
- 用户具有维护管理权限

### 核心流程

#### 2.1 维护计划制定流程
1. 选择需要制定计划的设备
2. 根据设备类型选择维护计划模板
3. 设置维护周期和维护内容
4. 分配维护责任人和执行团队
5. 设置维护提醒时间和方式
6. 审核维护计划的合理性
7. 提交维护计划审批
8. 审批通过后计划生效
9. 系统自动生成维护任务

#### 2.2 维护任务执行流程
1. 系统自动生成维护任务单
2. 发送维护任务通知给责任人
3. 维护人员接收任务并开始执行
4. 记录维护过程和发现的问题
5. 填写维护结果和设备状态
6. 上传维护照片和相关文档
7. 更新设备状态和下次维护时间
8. 提交维护完成报告
9. 维护主管审核确认

#### 2.3 故障维修处理流程
1. 接收设备故障报告
2. 评估故障严重程度和影响
3. 分配维修任务给合适的工程师
4. 诊断故障原因和制定维修方案
5. 申请维修备件和外部支持
6. 执行维修作业和测试验证
7. 记录维修过程和更换部件
8. 更新设备状态和维修记录
9. 分析故障原因和改进建议

### 后置条件
- 维护计划按时执行
- 维护记录完整准确
- 设备状态实时更新
- 维护成本准确统计

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：设备维护管理页面
### 页面目标：提供设备维护计划制定、任务执行和记录管理功能

### 信息架构：
- **顶部区域**：包含 任务搜索, 新建计划, 批量操作, 报表导出
- **左侧区域**：包含 设备分类, 维护类型, 状态筛选, 时间筛选
- **中间区域**：包含 维护任务列表, 任务详情, 执行界面
- **右侧区域**：包含 维护统计, 提醒事项, 快速操作

### 交互逻辑与状态：

#### **维护任务查询区域**
- **基础查询：**
  - **设备名称：** 输入框，支持模糊搜索
  - **任务编号：** 输入框，精确查询
  - **维护类型：** 下拉选择，预防性/纠正性/改进性
  - **任务状态：** 多选下拉，待执行/执行中/已完成/已逾期
- **高级筛选：**
  - **计划时间：** 日期范围选择器
  - **责任人：** 下拉选择维护人员
  - **设备分类：** 树形选择器
  - **优先级：** 下拉选择，高/中/低

#### **维护任务列表**
- **列表表头：**
  - **任务编号：** 可排序，点击查看详情
  - **设备名称：** 显示设备名称和编码
  - **维护类型：** 显示维护类型标签
  - **计划时间：** 显示计划执行时间
  - **责任人：** 显示维护责任人
  - **任务状态：** 状态标签
  - **优先级：** 优先级标识
  - **操作：** 执行、编辑、查看等操作
- **状态标识：**
  - **待执行：** 蓝色标签，"待执行"
  - **执行中：** 绿色标签，"执行中"
  - **已完成：** 灰色标签，"已完成"
  - **已逾期：** 红色标签，"已逾期"

#### **维护计划制定界面**
- **基本信息：**
  - **设备选择：** 搜索选择器，选择目标设备
  - **维护类型：** 单选按钮，预防性/纠正性/改进性
  - **维护周期：** 下拉选择，日/周/月/季/年
  - **计划开始时间：** 日期时间选择器
- **维护内容：**
  - **维护项目：** 复选框组，选择维护项目
  - **维护标准：** 文本域，维护标准和要求
  - **所需工时：** 数字输入框，预计工时
  - **所需备件：** 表格编辑，备件清单
- **人员安排：**
  - **主要责任人：** 下拉选择，必填
  - **协助人员：** 多选框，选择协助人员
  - **外部支持：** 输入框，外部技术支持
  - **技能要求：** 文本域，技能要求说明

#### **维护任务执行界面**
- **任务信息：**
  - **任务编号：** 只读显示，系统生成
  - **设备信息：** 只读显示，设备基本信息
  - **维护内容：** 只读显示，维护项目清单
  - **计划时间：** 只读显示，计划执行时间
- **执行记录：**
  - **实际开始时间：** 日期时间选择器
  - **实际结束时间：** 日期时间选择器
  - **执行人员：** 多选框，实际参与人员
  - **维护过程：** 文本域，详细维护过程
- **结果记录：**
  - **维护结果：** 单选按钮，正常/异常/需要进一步处理
  - **发现问题：** 文本域，维护中发现的问题
  - **处理措施：** 文本域，问题处理措施
  - **改进建议：** 文本域，维护改进建议
- **资源消耗：**
  - **使用备件：** 表格编辑，实际使用的备件
  - **维护成本：** 数字输入框，维护总成本
  - **工时统计：** 数字输入框，实际工时
  - **外部费用：** 数字输入框，外部服务费用

#### **维护历史管理**
- **历史记录：**
  - **维护时间：** 显示维护执行时间
  - **维护类型：** 显示维护类型
  - **执行人员：** 显示维护人员
  - **维护结果：** 显示维护结果
  - **维护成本：** 显示维护费用
- **趋势分析：**
  - **维护频率：** 图表显示维护频率趋势
  - **成本趋势：** 图表显示维护成本变化
  - **故障率：** 图表显示设备故障率
  - **效果评估：** 显示维护效果评估

#### **维护提醒管理**
- **提醒设置：**
  - **提醒时间：** 下拉选择，提前天数
  - **提醒方式：** 复选框，邮件/短信/系统通知
  - **提醒对象：** 多选框，提醒人员
  - **提醒内容：** 文本域，提醒内容模板
- **提醒列表：**
  - **设备名称：** 显示需要维护的设备
  - **维护类型：** 显示维护类型
  - **计划时间：** 显示计划维护时间
  - **剩余天数：** 显示距离维护的天数
  - **操作：** 执行维护、延期等操作

### 数据校验规则：

#### **维护计划**
- **校验规则：** 维护周期必须大于0，计划时间不能早于当前时间
- **错误提示文案：** "维护计划时间设置不合理，请重新设置"

#### **执行记录**
- **校验规则：** 实际结束时间必须晚于开始时间，维护结果必须选择
- **错误提示文案：** "维护执行记录不完整，请填写完整信息"

#### **成本统计**
- **校验规则：** 维护成本必须大于等于0，备件消耗数量必须为正数
- **错误提示文案：** "维护成本数据不正确，请检查输入"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **维护计划**:
  - **设备ID (equipment_id)**: String, 必填, 引用设备档案
  - **维护类型 (maintenance_type)**: String, 必填, 预防性/纠正性/改进性
  - **维护周期 (cycle)**: Integer, 必填, 维护周期天数
  - **责任人ID (responsible_id)**: String, 必填, 引用员工主数据
- **执行记录**:
  - **实际开始时间 (actual_start_time)**: DateTime, 必填
  - **实际结束时间 (actual_end_time)**: DateTime, 必填
  - **维护结果 (result)**: String, 必填, 正常/异常/需要进一步处理
  - **维护成本 (cost)**: Decimal, 必填, 大于等于0

### 展示数据
- **维护任务列表**: 任务编号、设备信息、维护类型、状态、责任人
- **维护历史**: 历史维护记录、成本统计、趋势分析
- **提醒事项**: 即将到期的维护任务和逾期任务
- **统计报表**: 维护成本、工时统计、效果评估

### 空状态/零数据
- **无维护任务**: 显示"暂无维护任务，点击新建计划开始"
- **无维护历史**: 显示"该设备暂无维护历史记录"
- **无提醒事项**: 显示"暂无维护提醒事项"

### API接口
- **维护计划**: GET/POST/PUT /api/maintenance/plans
- **维护任务**: GET/POST/PUT /api/maintenance/tasks
- **执行记录**: POST /api/maintenance/records
- **成本统计**: GET /api/maintenance/costs
- **提醒管理**: GET/POST /api/maintenance/reminders

## 5. 异常与边界处理 (Error & Edge Cases)

### **设备状态冲突**
- **提示信息**: "设备当前状态不允许执行维护，请检查设备状态"
- **用户操作**: 提供设备状态查看和状态变更选项

### **维护人员冲突**
- **提示信息**: "维护人员在该时间段已有其他任务安排"
- **用户操作**: 提供人员调度建议和时间调整选项

### **备件库存不足**
- **提示信息**: "维护所需备件库存不足，请先补充库存"
- **用户操作**: 提供备件采购申请和替代方案

### **维护逾期处理**
- **提示信息**: "维护任务已逾期，可能影响设备正常运行"
- **用户操作**: 提供紧急维护和风险评估选项

### **数据同步失败**
- **提示信息**: "维护数据同步失败，请检查网络连接"
- **用户操作**: 提供手动同步和离线模式选项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 维护计划制定功能完整，支持多种维护类型
- [ ] 维护任务自动生成和分配功能正常
- [ ] 维护执行记录功能完善，数据完整
- [ ] 维护提醒功能有效，通知及时
- [ ] 维护历史查询和统计功能准确
- [ ] 维护成本统计和分析功能完整
- [ ] 与设备档案系统(EMS-001)集成正常
- [ ] 与备件管理系统(EMS-003)集成正常
- [ ] 数据校验规则完善，错误提示友好
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 操作日志完整，支持审计追踪

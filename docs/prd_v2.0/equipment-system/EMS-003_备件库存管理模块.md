# 功能模块规格说明书：备件库存管理模块

- **模块ID**: EMS-003
- **所属子系统**: 设备管理子系统(Equipment Management System)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 备件管理员, **I want to** 管理备件档案和库存信息, **so that** 确保维护所需备件的及时供应。
- **As a** 维护工程师, **I want to** 查询备件库存和申请备件, **so that** 顺利完成设备维护工作。
- **As a** 采购专员, **I want to** 根据备件消耗和库存情况制定采购计划, **so that** 优化库存成本和保障供应。
- **As a** 仓库管理员, **I want to** 管理备件入库、出库和盘点, **so that** 确保库存数据的准确性。
- **As a** 财务人员, **I want to** 统计备件成本和消耗分析, **so that** 进行成本控制和预算管理。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 设备档案已在EMS-001中建立
- 供应商主数据已在系统中建立
- 仓库信息已配置
- 用户具有备件管理权限

### 核心流程

#### 2.1 备件档案建立流程
1. 创建备件基本信息和编码
2. 设置备件分类和技术规格
3. 关联适用设备和设备型号
4. 设置供应商信息和采购参数
5. 配置库存控制参数
6. 设置备件存储要求和位置
7. 上传备件图片和技术资料
8. 审核备件档案信息
9. 备件档案生效并可用

#### 2.2 备件入库管理流程
1. 接收备件采购订单信息
2. 验收备件质量和数量
3. 录入备件入库信息
4. 分配备件存储位置
5. 更新备件库存数量
6. 生成入库单据和标签
7. 记录入库成本和供应商
8. 更新备件档案信息
9. 发送入库完成通知

#### 2.3 备件出库申请流程
1. 维护人员提交备件申请
2. 系统检查备件库存可用性
3. 备件管理员审核申请
4. 分配备件并生成出库单
5. 仓库人员准备备件
6. 维护人员确认领取
7. 更新备件库存数量
8. 记录备件使用信息
9. 关联维护任务记录

#### 2.4 库存盘点管理流程
1. 制定盘点计划和范围
2. 生成盘点清单和任务
3. 执行实地盘点作业
4. 录入盘点结果数据
5. 分析盘点差异原因
6. 调整库存数据
7. 生成盘点报告
8. 审核盘点结果
9. 更新库存账面数据

### 后置条件
- 备件库存数据准确
- 备件申请及时响应
- 库存成本有效控制
- 备件消耗记录完整

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：备件库存管理页面
### 页面目标：提供备件档案管理、库存控制和申请审批功能

### 信息架构：
- **顶部区域**：包含 备件搜索, 新建备件, 批量操作, 报表导出
- **左侧区域**：包含 备件分类, 库存状态, 仓库筛选, 设备关联
- **中间区域**：包含 备件列表, 备件详情, 库存操作
- **右侧区域**：包含 库存统计, 预警信息, 快速操作

### 交互逻辑与状态：

#### **备件查询区域**
- **基础查询：**
  - **备件名称：** 输入框，支持模糊搜索
  - **备件编码：** 输入框，精确查询
  - **备件型号：** 输入框，支持模糊搜索
  - **库存状态：** 下拉选择，正常/不足/缺货/超储
- **高级筛选：**
  - **备件分类：** 树形选择器，支持多选
  - **适用设备：** 搜索选择器，选择设备
  - **供应商：** 下拉选择器，支持搜索
  - **存储仓库：** 下拉选择存储位置

#### **备件列表区域**
- **列表表头：**
  - **备件编码：** 可排序，点击查看详情
  - **备件名称：** 显示备件名称和规格
  - **备件分类：** 显示分类路径
  - **当前库存：** 显示库存数量和单位
  - **安全库存：** 显示安全库存水平
  - **库存状态：** 状态标签
  - **最后更新：** 显示最后库存变动时间
  - **操作：** 入库、出库、调整等操作
- **库存状态标识：**
  - **正常：** 绿色标签，"库存正常"
  - **不足：** 橙色标签，"库存不足"
  - **缺货：** 红色标签，"缺货"
  - **超储：** 蓝色标签，"库存超储"

#### **备件档案创建/编辑界面**
- **基本信息：**
  - **备件编码：** 输入框，自动生成或手工输入，必填
  - **备件名称：** 输入框，必填，最大100字符
  - **备件规格：** 输入框，必填，技术规格描述
  - **备件分类：** 树形选择器，必填
- **技术参数：**
  - **材质：** 输入框，备件材质信息
  - **尺寸：** 输入框，长×宽×高格式
  - **重量：** 数字输入框，单位kg
  - **技术标准：** 输入框，执行标准
- **设备关联：**
  - **适用设备：** 多选框，选择适用设备
  - **设备型号：** 多选框，选择设备型号
  - **关键程度：** 单选按钮，关键/重要/一般
  - **替代备件：** 搜索选择器，选择替代备件
- **供应商信息：**
  - **主供应商：** 下拉搜索选择器，必填
  - **备用供应商：** 多选框，选择备用供应商
  - **采购周期：** 数字输入框，单位天
  - **最小订购量：** 数字输入框，最小采购数量

#### **库存控制参数**
- **库存设置：**
  - **安全库存：** 数字输入框，安全库存数量
  - **最大库存：** 数字输入框，最大库存限制
  - **补货点：** 数字输入框，自动补货触发点
  - **补货数量：** 数字输入框，建议补货数量
- **存储要求：**
  - **存储仓库：** 下拉选择存储仓库
  - **存储位置：** 输入框，具体存储位置
  - **存储条件：** 下拉选择，常温/低温/干燥/防潮
  - **保质期：** 数字输入框，保质期月数
- **成本信息：**
  - **标准成本：** 数字输入框，标准单价
  - **最新采购价：** 数字输入框，最新采购单价
  - **平均成本：** 只读显示，系统计算
  - **库存价值：** 只读显示，当前库存价值

#### **库存操作界面**
- **入库操作：**
  - **入库类型：** 单选按钮，采购入库/调拨入库/其他入库
  - **入库数量：** 数字输入框，必填，大于0
  - **入库单价：** 数字输入框，入库单价
  - **供应商：** 下拉选择器，入库供应商
  - **入库日期：** 日期选择器，默认当前日期
  - **质检状态：** 单选按钮，合格/不合格/待检
  - **入库说明：** 文本域，入库原因说明
- **出库操作：**
  - **出库类型：** 单选按钮，维护领用/调拨出库/其他出库
  - **出库数量：** 数字输入框，必填，不超过可用库存
  - **领用人：** 搜索选择器，选择领用人员
  - **用途说明：** 文本域，出库用途说明
  - **关联任务：** 搜索选择器，关联维护任务
- **库存调整：**
  - **调整类型：** 单选按钮，盘盈/盘亏/损耗/其他
  - **调整数量：** 数字输入框，可正可负
  - **调整原因：** 下拉选择预设原因
  - **调整说明：** 文本域，详细调整说明
  - **审批人：** 搜索选择器，选择审批人

#### **库存统计分析**
- **库存概况：**
  - **备件总数：** 显示备件品种总数
  - **库存总值：** 显示库存总价值
  - **周转率：** 显示库存周转率
  - **缺货率：** 显示缺货备件比例
- **预警信息：**
  - **库存不足：** 列表显示库存不足的备件
  - **即将过期：** 列表显示即将过期的备件
  - **长期滞销：** 列表显示长期无消耗的备件
  - **超储预警：** 列表显示库存超储的备件
- **消耗分析：**
  - **月度消耗：** 图表显示月度消耗趋势
  - **分类消耗：** 饼图显示各分类消耗占比
  - **设备消耗：** 柱状图显示各设备备件消耗
  - **成本分析：** 图表显示备件成本变化

#### **备件申请管理**
- **申请创建：**
  - **申请备件：** 搜索选择器，选择需要的备件
  - **申请数量：** 数字输入框，申请数量
  - **紧急程度：** 单选按钮，紧急/普通/计划
  - **用途说明：** 文本域，使用用途说明
  - **预计使用时间：** 日期时间选择器
- **申请审批：**
  - **申请信息：** 只读显示申请详情
  - **库存检查：** 显示当前库存状态
  - **审批意见：** 单选按钮，同意/拒绝/部分同意
  - **审批说明：** 文本域，审批意见说明
  - **替代方案：** 输入框，替代备件建议

### 数据校验规则：

#### **备件编码**
- **校验规则：** 必须唯一，符合编码规则格式
- **错误提示文案：** "备件编码已存在，请重新输入"

#### **库存数量**
- **校验规则：** 必须为非负数，出库数量不能超过可用库存
- **错误提示文案：** "库存数量不能为负数"

#### **成本价格**
- **校验规则：** 必须大于0，价格格式正确
- **错误提示文案：** "请输入正确的价格信息"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **备件档案**:
  - **备件编码 (spare_code)**: String, 必填, 全局唯一
  - **备件名称 (spare_name)**: String, 必填, 最大100字符
  - **备件规格 (specification)**: String, 必填, 最大200字符
  - **备件分类ID (category_id)**: String, 必填, 引用备件分类
- **库存信息**:
  - **当前库存 (current_stock)**: Integer, 必填, 大于等于0
  - **安全库存 (safety_stock)**: Integer, 必填, 大于等于0
  - **最大库存 (max_stock)**: Integer, 可选, 大于安全库存
- **成本信息**:
  - **标准成本 (standard_cost)**: Decimal, 必填, 大于0
  - **最新采购价 (latest_price)**: Decimal, 可选, 大于0

### 展示数据
- **备件列表**: 备件编码、名称、规格、库存、状态
- **库存统计**: 库存总值、周转率、缺货率、预警信息
- **消耗分析**: 消耗趋势、分类分析、成本分析
- **申请记录**: 备件申请、审批状态、领用记录

### 空状态/零数据
- **无备件数据**: 显示"暂无备件档案，点击新建备件开始"
- **无库存记录**: 显示"暂无库存变动记录"
- **无申请记录**: 显示"暂无备件申请记录"

### API接口
- **备件档案**: GET/POST/PUT /api/spares
- **库存操作**: POST /api/spares/stock
- **备件申请**: GET/POST/PUT /api/spares/requests
- **库存统计**: GET /api/spares/statistics
- **消耗分析**: GET /api/spares/consumption

## 5. 异常与边界处理 (Error & Edge Cases)

### **库存不足**
- **提示信息**: "备件库存不足，无法满足申请数量"
- **用户操作**: 提供采购建议和替代备件选项

### **备件过期**
- **提示信息**: "备件已过保质期，不建议使用"
- **用户操作**: 提供处置建议和替换选项

### **供应商停产**
- **提示信息**: "备件供应商已停产，请寻找替代供应商"
- **用户操作**: 提供替代供应商和替代备件建议

### **库存数据异常**
- **提示信息**: "库存数据异常，请进行库存盘点"
- **用户操作**: 提供盘点功能和数据修正选项

### **权限不足**
- **提示信息**: "您没有权限执行此操作"
- **用户操作**: 显示所需权限和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 备件档案管理功能完整，信息录入便捷
- [ ] 库存控制参数设置合理，预警功能有效
- [ ] 入库出库操作流程清晰，数据准确
- [ ] 备件申请审批流程完善，响应及时
- [ ] 库存统计分析功能准确，报表完整
- [ ] 与设备档案系统(EMS-001)集成正常
- [ ] 与维护管理系统(EMS-002)集成正常
- [ ] 与采购系统集成，支持自动补货
- [ ] 数据校验规则完善，错误提示友好
- [ ] 所有页面元素符合全局设计规范
- [ ] 响应时间<3秒，支持并发操作
- [ ] 操作日志完整，支持审计追踪

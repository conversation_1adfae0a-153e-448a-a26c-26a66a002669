# PRD-04: 采购管理子系统 产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**传统手动采购模式计算量大、易出错，无法精确匹配生产需求，导致库存积压或停工待料，严重影响生产效率和成本控制。**

### 1.2 价值主张
建立数据驱动的智能采购管理平台，通过MRP自动计算和供应链协同，实现按需采购、精准交付和成本优化。

### 1.3 商业价值量化
- **库存成本降低**: MRP精确计算使库存资金占用减少30%
- **缺料风险降低**: 自动化需求计算使生产停工风险降低90%
- **采购效率提升**: 自动化流程使采购处理效率提升80%
- **供应商协同**: 标准化流程使供应商响应时间缩短50%

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **采购员** | 负责日常采购订单处理、供应商沟通 | 需要高效的订单管理工具和供应商信息 |
| **采购经理** | 管理采购团队、制定采购策略、审核大额订单 | 需要采购分析工具和审批管理功能 |
| **计划员** | 负责MRP运算、需求计划制定 | 需要准确的需求计算工具和计划管理 |

### 2.2 核心使用场景

#### 场景一：MRP自动生成采购计划
**用户故事**: 作为一个计划员，我想要通过MRP自动计算物料需求，以便准确知道需要采购什么、何时采购。

**操作流程**:
1. 选择需要运算的销售订单范围
2. 系统自动获取订单关联的生产BOM
3. 计算毛需求：订单需求量 × BOM用量
4. 计算净需求：毛需求 - 可用库存 - 在途采购
5. 生成采购建议清单，包含物料、数量、期望到货日期
6. 计划员审核并调整采购建议

**成功标准**: MRP运算准确率≥99%，100个订单运算时间<2分钟

#### 场景二：采购订单智能生成
**用户故事**: 作为一个采购员，我想要将采购建议快速转为采购订单，以便高效完成采购任务。

**操作流程**:
1. 在采购建议列表中勾选需要采购的物料
2. 系统按供应商自动分组并创建采购订单草稿
3. 确认供应商、价格、数量、交期等信息
4. 提交采购订单进入审批流程
5. 审批通过后发送给供应商
6. 跟踪订单状态直至收货完成

**成功标准**: 采购建议转订单成功率100%，订单处理效率提升80%

#### 场景三：工序外协精准管理
**用户故事**: 作为一个采购员，我想要准确管理工序外协业务，以便清晰追踪发出物料和返回成品。

**操作流程**:
1. 生产订单中的外协工序触发外协采购申请
2. 采购员将申请转为工序外协采购订单
3. 仓库根据外协订单执行"外协发货"
4. 系统记录委外在途物资，不减少库存
5. 外协厂完成加工后执行"外协收货"
6. 质检合格后入库，完成外协流程

**成功标准**: 外协物料追踪准确率100%，外协流程标准化

---

## 3. 功能需求（用户故事格式）

### 3.1 供应商管理

#### 需求 3.1.1: 供应商档案管理
**用户故事**: 作为一个采购经理，我想要维护完整的供应商信息库，以便进行有效的供应商关系管理。

**功能描述**:
- 供应商基本信息、财务信息、资质文件管理
- 供应商评级和分类管理
- 供应商历史交易记录和绩效评估
- 供应商准入和退出流程

**验收标准**:
- [ ] 支持供应商的创建、编辑、查询、禁用操作
- [ ] 供应商信息包含基本信息、财务信息、资质文件
- [ ] 支持供应商评级（A/B/C类）和分类管理
- [ ] 关键信息（名称、统一社会信用代码）唯一性校验
- [ ] 供应商变更记录完整的审计轨迹

#### 需求 3.1.2: 供应商绩效管理
**用户故事**: 作为一个采购经理，我想要评估供应商绩效，以便优化供应商结构。

**功能描述**:
- 供应商交付及时率、质量合格率统计
- 供应商价格竞争力分析
- 供应商风险评估和预警
- 供应商绩效报告和排名

**验收标准**:
- [ ] 自动统计供应商交付及时率和质量合格率
- [ ] 支持供应商价格对比分析
- [ ] 提供供应商风险预警机制
- [ ] 生成供应商绩效评估报告

### 3.2 物料需求计划（MRP）

#### 需求 3.2.1: MRP核心运算
**用户故事**: 作为一个计划员，我想要运行MRP自动计算物料净需求，以便准确制定采购计划。

**功能描述**:
- 基于销售订单和生产BOM的需求计算
- 考虑现有库存、在途采购、安全库存的净需求计算
- 支持按订单、按时间、按物料等维度运算
- 生成详细的采购建议清单

**验收标准**:
- [ ] MRP运算逻辑：净需求 = (销售订单需求 + 安全库存) - (现有库存 + 在途采购)
- [ ] 支持多维度的MRP运算范围选择
- [ ] 运算结果包含物料、需求数量、建议供应商、期望到货日期
- [ ] 100个订单（平均每个BOM 10行）运算时间 < 2分钟
- [ ] 运算准确率 ≥ 99%

#### 需求 3.2.2: 采购建议管理
**用户故事**: 作为一个计划员，我想要管理和调整MRP生成的采购建议，以便优化采购计划。

**功能描述**:
- 采购建议的查看、编辑、合并、拆分
- 采购建议的批量操作和状态管理
- 采购建议转采购订单的批量处理
- 采购建议的历史记录和追踪

**验收标准**:
- [ ] 支持采购建议的修改、合并、拆分操作
- [ ] 支持采购建议的批量选择和处理
- [ ] 物料缺少采购提前期或供应商时明确提示
- [ ] 采购建议状态清晰，支持批量转订单

### 3.3 采购订单管理

#### 需求 3.3.1: 采购订单全生命周期管理
**用户故事**: 作为一个采购员，我想要方便地创建和管理采购订单，以便高效完成采购任务。

**功能描述**:
- 支持从采购建议自动生成和手动创建采购订单
- 完整的订单状态管理和流程控制
- 采购订单的打印、导出和电子化发送
- 订单变更和取消的流程管理

**验收标准**:
- [ ] 支持从采购建议自动生成和手动创建两种方式
- [ ] 订单状态包含：草稿、待审批、已批准、已发货、部分收货、全部收货、已完成
- [ ] 支持采购订单的打印和电子化发送
- [ ] 订单变更需要审批流程控制

#### 需求 3.3.2: 灵活审批流程
**用户故事**: 作为一个采购经理，我想要配置灵活的采购审批流程，以便控制采购风险。

**功能描述**:
- 可配置的审批流程设置
- 按金额分级的审批规则
- 审批流程的自动匹配和执行
- 审批过程的跟踪和提醒

**验收标准**:
- [ ] 支持按金额区间配置不同的审批路径
- [ ] 提交订单时自动匹配对应的审批流程
- [ ] 采购价格高于参考价时审批高亮提醒
- [ ] 找不到匹配审批流程时禁止提交并提示

### 3.4 外协管理

#### 需求 3.4.1: 外协业务分类管理
**用户故事**: 作为一个采购员，我想要区分管理部件外购和工序外协，以便准确处理不同类型的采购。

**功能描述**:
- 部件外购：标准采购流程，直接入库增加库存
- 工序外协：委外加工流程，管理委外在途物资
- 外协订单的专门管理和跟踪
- 外协发货和收货的特殊处理

**验收标准**:
- [ ] 明确区分部件外购和工序外协两种业务类型
- [ ] 工序外协订单关联发出半成品和收回成品
- [ ] 外协发货不减少库存，增加"委外在途"数量
- [ ] 外协收货时数量不符提示并要求处理

#### 需求 3.4.2: 外协物料追踪
**用户故事**: 作为一个采购员，我想要准确追踪外协物料流向，以便确保外协业务的透明度。

**功能描述**:
- 外协物料的发出和返回全程跟踪
- 委外在途物资的实时监控
- 外协加工进度的可视化展示
- 外协异常情况的预警和处理

**验收标准**:
- [ ] 外协物料发出、在途、返回状态清晰可追踪
- [ ] 委外在途物资数量实时准确
- [ ] 外协超期预警和异常处理机制
- [ ] 外协物料损耗的记录和处理

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] MRP运算准确率 ≥ 99%
- [ ] 采购建议转订单成功率 100%
- [ ] 外协物料追踪准确率 100%
- [ ] 供应商信息管理完整性 100%
- [ ] 审批流程执行准确率 100%

### 4.2 性能验收标准
- [ ] MRP运算时间：100个订单 < 2分钟
- [ ] 采购订单创建响应时间 < 3秒
- [ ] 供应商查询响应时间 < 1秒
- [ ] 采购建议生成时间 < 30秒
- [ ] 系统并发处理能力 ≥ 50用户

### 4.3 业务效果验收标准
- [ ] 库存周转率提升 ≥ 30%
- [ ] 采购处理效率提升 ≥ 80%
- [ ] 缺料导致的停工时间减少 ≥ 90%
- [ ] 供应商响应时间缩短 ≥ 50%

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **数据驱动**: 关键数据和指标突出显示，支持快速决策
- **流程清晰**: 采购流程步骤明确，状态变化及时反馈
- **批量操作**: 支持批量处理，提高操作效率
- **异常突出**: 异常情况醒目提示，便于快速处理

### 5.2 关键界面要求
- **MRP运算界面**: 参数设置简单，结果展示清晰
- **采购建议列表**: 支持多维度筛选和批量操作
- **采购订单管理**: 状态流转清晰，操作便捷
- **供应商管理**: 信息完整，绩效可视化

---

## 6. 数据埋点需求

### 6.1 业务行为埋点
- MRP运算频率和准确性
- 采购订单创建和审批行为
- 供应商选择和变更行为
- 外协业务处理行为

### 6.2 性能监控埋点
- MRP运算耗时
- 采购订单处理时间
- 供应商响应时间
- 系统操作响应时间

### 6.3 业务效果埋点
- 库存周转率变化
- 采购成本变化
- 供应商绩效指标
- 缺料风险指标

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **AI智能采购**: 基于历史数据的智能采购建议
- **供应商门户**: 供应商自助服务平台
- **采购电商化**: 在线采购商城和竞价平台
- **高级分析**: 采购数据的深度分析和预测

### 7.2 技术演进方向
- **实时协同**: 与供应商的实时数据协同
- **移动采购**: 移动端的采购审批和管理
- **区块链溯源**: 采购物料的区块链溯源

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅

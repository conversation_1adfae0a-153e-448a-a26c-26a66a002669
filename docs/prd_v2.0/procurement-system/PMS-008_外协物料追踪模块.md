# 功能模块规格说明书：外协物料追踪模块

- **模块ID**: PMS-008
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 生产计划员, **I want to** 实时追踪外协物料位置和状态, **so that** 准确掌握生产资源和进度。
- **As a** 采购员, **I want to** 监控委外在途物资, **so that** 及时发现和处理外协异常。
- **As a** 仓库管理员, **I want to** 跟踪外协物料流向, **so that** 确保物料账实一致。
- **As a** 财务人员, **I want to** 核算外协物料成本, **so that** 准确计算产品成本和外协费用。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 外协订单已创建并执行
- 物料编码体系已建立
- 外协供应商已配置
- 追踪节点已定义

### 核心流程

#### 2.1 外协物料出库追踪流程
1. 外协订单确认后生成出库任务
2. 仓库按订单备货并扫码出库
3. 系统记录物料出库信息：
   - 物料编码和批次号
   - 出库数量和时间
   - 目标外协供应商
   - 运输方式和预计到达时间
4. 生成外协物料追踪记录
5. 物料状态更新为"外协在途"

#### 2.2 外协加工过程追踪流程
1. 外协供应商接收物料并确认
2. 开始外协加工并定期更新状态：
   - 加工开始：记录开始时间和工序
   - 加工进行中：更新完成百分比
   - 质量检验：记录检验结果
   - 加工完成：确认完成时间和数量
3. 系统实时更新物料追踪状态
4. 异常情况及时记录和预警

#### 2.3 外协物料回流追踪流程
1. 外协加工完成后准备发货
2. 外协供应商扫码发货并更新状态
3. 物料运输过程中的位置追踪
4. 到达公司仓库后扫码收货
5. 质检合格后办理入库手续
6. 更新物料状态为"已入库"
7. 完成外协物料全程追踪

#### 2.4 外协物料异常处理流程
1. 系统监控外协物料状态异常
2. 触发异常预警机制：
   - 超时未到达外协供应商
   - 加工进度严重延误
   - 质检不合格需要返工
   - 物料丢失或损坏
3. 发送异常通知给相关人员
4. 采购员跟进异常处理
5. 记录异常处理过程和结果

### 后置条件
- 外协物料追踪记录完整
- 物料状态实时准确
- 异常问题及时处理
- 成本核算数据准确

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：外协物料追踪页面
### 页面目标：提供全面的外协物料追踪和监控界面

### 信息架构：
- **顶部区域**：包含 追踪概览统计, 快捷筛选器, 实时监控面板
- **中间区域**：包含 物料追踪列表, 追踪详情面板, 流向地图
- **底部区域**：包含 异常预警, 分页控件, 导出功能

### 交互逻辑与状态：

#### **追踪概览统计**
- **统计卡片组：** 5个关键指标卡片
  - **在途物料：** 蓝色数字，显示外协在途物料数量
  - **加工中物料：** 绿色数字，显示正在加工的物料
  - **异常物料：** 红色数字，显示异常状态物料
  - **今日回流：** 橙色数字，显示今日预计回流物料
  - **总价值：** 紫色数字，显示在途物料总价值
- **物料流向图表：** 饼图显示物料在各环节的分布

#### **实时监控面板**
- **状态指示灯：** 绿色/黄色/红色指示系统运行状态
- **最新动态：** 滚动显示最新的物料状态变更
- **预警提醒：** 红色闪烁显示紧急预警信息
- **刷新控制：** 自动刷新开关和手动刷新按钮

#### **快捷筛选器**
- **物料状态：** 标签页形式（全部/在途/加工中/已完成/异常）
- **外协供应商：** 下拉多选，筛选外协供应商
- **时间范围：** 日期范围选择器，筛选出库时间
- **物料类型：** 下拉选择，按物料分类筛选
- **紧急程度：** 复选框组（紧急/正常/低优先级）

#### **物料追踪列表**
- **表格样式：** 固定表头，实时更新，支持排序
- **列定义：**
  - 追踪编号：系统生成，链接查看详情
  - 物料编码：链接，查看物料信息
  - 物料名称：显示规格和批次
  - 外协订单：链接，查看订单详情
  - 外协供应商：显示供应商名称
  - 当前状态：状态标签，实时更新
  - 当前位置：显示物料当前位置
  - 出库时间：相对时间显示
  - 预计回流：日期显示，逾期红色
  - 完成进度：进度条显示
  - 操作：查看、更新、预警

#### **物料状态标识**
- **已出库：** 蓝色标签(#1890FF)，"已出库"
- **运输中：** 紫色标签(#722ED1)，"运输中"
- **已到达：** 绿色标签(#52C41A)，"已到达"
- **加工中：** 橙色标签(#FAAD14)，"加工中"
- **质检中：** 蓝色标签(#1890FF)，"质检中"
- **待发货：** 橙色标签(#FAAD14)，"待发货"
- **回流中：** 紫色标签(#722ED1)，"回流中"
- **已入库：** 深绿色标签(#389E0D)，"已入库"
- **异常：** 红色标签(#F5222D)，"异常"

#### **追踪详情面板**
- **基本信息卡片：**
  - 追踪编号：大号字体显示
  - 物料信息：编码、名称、规格、批次
  - 外协订单：订单号和外协供应商
  - 出库信息：出库时间、数量、仓库
- **当前状态卡片：**
  - 当前位置：突出显示当前位置
  - 状态描述：详细状态说明
  - 更新时间：最后更新时间
  - 负责人：当前环节负责人
- **进度时间轴：**
  - 时间节点：显示各个关键时间点
  - 状态变更：显示状态变更历史
  - 预计时间：显示各环节预计时间
  - 实际时间：显示实际完成时间

#### **物料流向地图**
- **地图显示：** 显示物料从公司到外协供应商的流向
- **位置标记：** 标记当前物料位置
- **路径显示：** 显示物料运输路径
- **状态图标：** 不同颜色图标表示不同状态
- **实时更新：** 位置信息实时更新

#### **追踪时间轴**
- **垂直时间轴：** 显示物料追踪的完整历程
- **关键节点：** 突出显示重要时间节点
- **状态变更：** 显示每次状态变更的详细信息
- **异常标记：** 红色标记异常事件
- **预计vs实际：** 对比预计时间和实际时间

#### **异常预警面板**
- **预警级别：**
  - **紧急：** 红色背景(#FFF2F0)，红色图标，闪烁效果
  - **重要：** 橙色背景(#FFF7E6)，橙色图标
  - **一般：** 黄色背景(#FFFBE6)，黄色图标
- **预警内容：** 物料信息、异常类型、影响程度
- **处理状态：** 未处理/处理中/已处理
- **操作按钮：** 查看详情、标记处理、忽略预警

#### **物料状态更新对话框**
- **当前状态显示：** 只读显示当前状态
- **新状态选择：** 下拉选择新的状态
- **位置信息：** 输入当前位置信息
- **进度更新：** 滑块控件设置完成百分比
- **备注说明：** 文本区域，填写状态变更说明
- **更新时间：** 自动填充当前时间
- **确认更新按钮：** 绿色按钮，提交状态更新

#### **批量操作工具栏**
- **全选复选框：** 控制列表项的全选/取消全选
- **批量更新：** 蓝色按钮，批量更新物料状态
- **批量预警：** 橙色边框按钮，批量设置预警
- **批量导出：** 灰色边框按钮，导出追踪信息
- **批量通知：** 绿色边框按钮，批量发送通知

#### **追踪报告生成**
- **报告类型：** 单选按钮（详细报告/汇总报告/异常报告）
- **时间范围：** 日期范围选择器
- **包含内容：** 复选框组，选择报告内容
- **输出格式：** 单选按钮（PDF/Excel/Word）
- **生成报告按钮：** 蓝色按钮，生成追踪报告

#### **物料成本核算**
- **成本构成：** 显示物料成本、外协费用、运输费用
- **成本变化：** 显示成本变化趋势
- **费用明细：** 表格显示详细费用项目
- **核算结果：** 显示最终成本核算结果

### 数据校验规则：

#### **状态更新**
- **校验规则：** 状态变更必须符合业务流程逻辑
- **错误提示文案：** "状态变更不符合业务流程，请检查"

#### **位置信息**
- **校验规则：** 位置信息不能为空，格式要求正确
- **错误提示文案：** "请填写正确的位置信息"

#### **进度更新**
- **校验规则：** 进度百分比必须在0-100之间，不能倒退
- **错误提示文案：** "进度百分比必须在0-100之间且不能倒退"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **状态更新**:
  - **新状态 (new_status)**: Enum, 必填, 预定义状态值
  - **当前位置 (current_location)**: String, 必填
  - **完成进度 (completion_progress)**: Number, 0-100
  - **备注说明 (remarks)**: String, 可选, 最大500字符
- **异常报告**:
  - **异常类型 (exception_type)**: Enum, 必填
  - **异常描述 (exception_description)**: String, 必填
  - **影响程度 (impact_level)**: Enum, [低/中/高]

### 展示数据
- **追踪概览**: 在途数量、状态分布、价值统计
- **物料列表**: 追踪号、物料信息、状态、位置、进度
- **追踪详情**: 完整的追踪信息和历史记录
- **流向地图**: 物料位置、运输路径、状态标识
- **异常预警**: 异常类型、影响程度、处理状态

### 空状态/零数据
- **无追踪记录**: 显示"暂无外协物料追踪记录"
- **无异常预警**: 显示"当前无异常预警，物料流转正常"
- **筛选无结果**: 显示"未找到匹配的追踪记录"

### API接口
- **获取追踪列表**: GET /api/material-tracking
- **更新物料状态**: PUT /api/material-tracking/{id}/status
- **获取追踪详情**: GET /api/material-tracking/{id}
- **创建异常报告**: POST /api/material-tracking/{id}/exception
- **生成追踪报告**: GET /api/material-tracking/report

## 5. 异常与边界处理 (Error & Edge Cases)

### **物料追踪中断**
- **提示信息**: "物料追踪信号中断，请联系外协供应商确认状态"
- **用户操作**: 显示最后已知位置，提供手动更新选项

### **状态更新冲突**
- **提示信息**: "物料状态已被其他人更新，请刷新后重试"
- **用户操作**: 自动刷新最新状态，显示冲突信息

### **异常物料处理**
- **提示信息**: "检测到异常物料，需要立即处理"
- **用户操作**: 高亮异常物料，提供处理流程指导

### **追踪数据丢失**
- **提示信息**: "部分追踪数据丢失，请补充相关信息"
- **用户操作**: 显示缺失数据项，提供数据补充界面

### **网络连接异常**
- **提示信息**: "网络连接异常，追踪数据可能不是最新"
- **用户操作**: 显示离线模式，提供手动同步功能

### **权限不足**
- **提示信息**: "您没有权限查看此物料的追踪信息"
- **用户操作**: 隐藏敏感信息，显示权限申请入口

## 6. 验收标准 (Acceptance Criteria)

- [ ] 实时追踪外协物料的位置和状态
- [ ] 支持物料从出库到入库的全程追踪
- [ ] 物料状态包含出库、运输、加工、质检、入库等环节
- [ ] 追踪信息实时更新，延迟不超过5分钟
- [ ] 异常预警机制完善，及时发现和处理问题
- [ ] 支持物料流向的可视化展示
- [ ] 追踪历史记录完整，支持审计追溯
- [ ] 支持多维度筛选和搜索功能
- [ ] 批量操作功能正常（更新、预警、导出）
- [ ] 追踪报告生成功能完善
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 追踪列表查询响应时间小于2秒
- [ ] 状态更新响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

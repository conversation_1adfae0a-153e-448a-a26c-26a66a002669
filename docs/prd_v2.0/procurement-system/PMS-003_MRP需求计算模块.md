# 功能模块规格说明书：MRP需求计算模块

- **模块ID**: PMS-003
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 计划员, **I want to** 运行MRP自动计算物料净需求, **so that** 准确制定采购计划。
- **As a** 计划员, **I want to** 选择不同的运算范围和参数, **so that** 灵活应对不同的计划需求。
- **As a** 计划员, **I want to** 查看详细的需求计算过程, **so that** 理解和验证计算结果的准确性。
- **As a** 采购员, **I want to** 获得准确的采购建议, **so that** 按需采购避免库存积压。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 销售订单数据已录入
- 产品BOM结构已建立
- 库存数据实时准确
- 在途采购数据已更新

### 核心流程

#### 2.1 MRP运算参数设置流程
1. 计划员进入MRP运算页面
2. 选择运算范围：
   - 按订单范围：选择特定销售订单
   - 按时间范围：选择计划时间段
   - 按产品范围：选择特定产品类别
3. 设置运算参数：
   - 计划时间栅栏：设置计划的时间精度
   - 安全库存策略：是否考虑安全库存
   - 采购提前期：设置默认采购周期
4. 确认运算参数并开始计算

#### 2.2 MRP核心运算流程
1. 系统获取选定范围内的销售订单
2. 展开产品BOM结构，计算毛需求：
   - 毛需求 = 订单需求量 × BOM用量系数
3. 获取当前库存数据和在途采购数据
4. 计算净需求：
   - 净需求 = 毛需求 + 安全库存 - 现有库存 - 在途采购
5. 根据采购提前期计算建议采购时间
6. 生成采购建议清单

#### 2.3 需求计算验证流程
1. 系统完成MRP运算后进行数据验证
2. 检查计算逻辑的一致性
3. 验证数据的完整性和准确性
4. 识别异常数据和潜在问题
5. 生成运算日志和异常报告
6. 向计划员展示验证结果

#### 2.4 运算结果分析流程
1. 计划员查看MRP运算结果
2. 分析需求分布和采购建议
3. 识别关键物料和紧急需求
4. 检查运算异常和数据问题
5. 调整运算参数重新计算（如需要）
6. 确认运算结果并生成采购建议

### 后置条件
- MRP运算结果准确生成
- 采购建议清单完整
- 运算日志记录完整
- 异常问题得到识别和处理

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：MRP需求计算页面
### 页面目标：提供高效准确的MRP运算界面

### 信息架构：
- **顶部区域**：包含 运算参数设置面板, 运算控制按钮, 运算状态指示器
- **中间区域**：包含 运算结果表格, 需求分析图表, 异常提醒面板
- **底部区域**：包含 运算日志, 操作历史, 导出功能

### 交互逻辑与状态：

#### **运算参数设置面板**
- **运算范围选择：**
  - **按订单：** 单选按钮，显示订单选择器
  - **按时间：** 单选按钮，显示日期范围选择器
  - **按产品：** 单选按钮，显示产品分类选择器
- **订单选择器：** 多选列表，支持搜索和筛选
- **时间范围选择：** 日期范围选择器，默认未来30天
- **产品分类选择：** 树形选择器，支持多选

#### **运算参数配置**
- **计划时间栅栏：** 下拉选择（天/周/月）
- **安全库存策略：** 开关控件，启用/禁用安全库存
- **采购提前期：** 数值输入框，默认7天
- **运算精度：** 下拉选择（精确/快速）
- **并行计算：** 开关控件，启用多线程计算

#### **运算控制按钮组**
- **开始运算按钮：**
  - **默认状态：** 绿色背景(#52C41A)，白色文字"开始运算"
  - **运算中状态：** 灰色背景，显示"运算中..."和进度条
  - **完成状态：** 蓝色背景，文字变为"重新运算"
- **停止运算按钮：** 红色边框，仅在运算中显示
- **清空结果按钮：** 灰色边框，清空上次运算结果

#### **运算状态指示器**
- **运算进度条：** 显示当前运算进度百分比
- **状态文字：** 显示当前运算阶段（数据准备/需求计算/结果生成）
- **预计时间：** 显示预计剩余时间
- **运算统计：** 显示已处理订单数/总订单数

#### **运算结果表格**
- **表格样式：** 固定表头，支持排序和筛选
- **列定义：**
  - 物料编码：链接，点击查看物料详情
  - 物料名称：显示完整名称
  - 毛需求：数值显示，单位标注
  - 现有库存：数值显示，颜色编码
  - 在途采购：数值显示，悬停显示详情
  - 安全库存：数值显示，可编辑
  - 净需求：突出显示，负数用红色
  - 建议采购量：绿色突出显示
  - 建议到货日期：日期显示
  - 建议供应商：下拉选择

#### **需求紧急程度标识**
- **紧急需求：** 红色标签(#F5222D)，"紧急"
- **正常需求：** 绿色标签(#52C41A)，"正常"
- **预警需求：** 橙色标签(#FAAD14)，"预警"
- **无需采购：** 灰色标签(#8C8C8C)，"充足"

#### **需求分析图表**
- **需求分布饼图：** 显示不同紧急程度需求的分布
- **时间轴甘特图：** 显示采购时间安排
- **库存趋势图：** 显示库存变化预测
- **图表交互：** 支持点击筛选和缩放

#### **异常提醒面板**
- **异常类型标识：**
  - 数据缺失：橙色图标，"数据缺失"
  - 计算异常：红色图标，"计算异常"
  - 逻辑冲突：黄色图标，"逻辑冲突"
- **异常详情：** 显示具体的异常描述和影响
- **处理建议：** 提供解决异常的具体建议
- **忽略按钮：** 允许忽略非关键异常

#### **运算结果筛选器**
- **需求类型筛选：** 复选框组（紧急/正常/预警/充足）
- **物料分类筛选：** 下拉多选，按产品分类筛选
- **供应商筛选：** 下拉多选，按建议供应商筛选
- **金额范围筛选：** 滑块控件，设置采购金额范围

#### **批量操作工具栏**
- **全选复选框：** 控制结果列表的全选/取消全选
- **批量调整：** 批量修改安全库存、供应商等
- **生成采购建议：** 将选中项转为采购建议
- **导出结果：** 导出运算结果为Excel文件

#### **运算日志面板**
- **日志级别：** 信息/警告/错误，不同颜色标识
- **时间戳：** 精确到秒的时间记录
- **操作描述：** 详细的操作和计算过程描述
- **数据统计：** 处理的数据量和耗时统计

### 数据校验规则：

#### **运算范围**
- **校验规则：** 必须选择至少一种运算范围
- **错误提示文案：** "请选择运算范围"

#### **时间范围**
- **校验规则：** 结束时间不能早于开始时间
- **错误提示文案：** "结束时间不能早于开始时间"

#### **采购提前期**
- **校验规则：** 必须为正整数，不超过365天
- **错误提示文案：** "采购提前期必须为1-365天的整数"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **运算参数**:
  - **运算范围类型 (scope_type)**: Enum, 必填, [订单/时间/产品]
  - **订单范围 (order_scope)**: Array, 可选, 订单ID列表
  - **时间范围 (time_scope)**: Object, 可选, {start_date, end_date}
  - **产品范围 (product_scope)**: Array, 可选, 产品分类ID列表
  - **采购提前期 (lead_time)**: Number, 必填, 默认7天
  - **安全库存策略 (safety_stock_enabled)**: Boolean, 默认true

### 展示数据
- **运算结果**: 物料编码、名称、毛需求、净需求、建议采购量
- **需求分析**: 需求分布、紧急程度统计、时间分布
- **库存状态**: 现有库存、在途采购、安全库存
- **异常信息**: 异常类型、描述、影响范围、处理建议
- **运算统计**: 处理时间、数据量、成功率

### 空状态/零数据
- **无销售订单**: 显示"选择范围内无销售订单数据"
- **无BOM数据**: 显示"产品BOM数据缺失，请先维护BOM"
- **运算无结果**: 显示"当前无需采购，库存充足"

### API接口
- **执行MRP运算**: POST /api/mrp/calculate
- **获取运算结果**: GET /api/mrp/results/{task_id}
- **获取运算进度**: GET /api/mrp/progress/{task_id}
- **导出运算结果**: GET /api/mrp/export/{task_id}
- **获取运算历史**: GET /api/mrp/history

## 5. 异常与边界处理 (Error & Edge Cases)

### **BOM数据缺失**
- **提示信息**: "产品BOM数据缺失，无法计算物料需求"
- **用户操作**: 显示缺失BOM的产品列表，提供BOM维护入口

### **库存数据不准确**
- **提示信息**: "检测到库存数据异常，可能影响计算准确性"
- **用户操作**: 显示异常库存数据，提供库存盘点建议

### **运算超时**
- **提示信息**: "MRP运算超时，建议缩小运算范围或选择快速模式"
- **用户操作**: 提供运算范围调整建议，支持分批运算

### **内存不足**
- **提示信息**: "数据量过大，系统资源不足，建议分批处理"
- **用户操作**: 自动建议合理的批次大小，支持分批运算

### **数据一致性错误**
- **提示信息**: "检测到数据一致性问题，请检查基础数据"
- **用户操作**: 显示数据一致性检查报告，提供修复建议

### **运算中断**
- **提示信息**: "MRP运算被中断，可以从断点继续运算"
- **用户操作**: 提供断点续算功能，保存运算进度

## 6. 验收标准 (Acceptance Criteria)

- [ ] MRP运算逻辑：净需求 = (销售订单需求 + 安全库存) - (现有库存 + 在途采购)
- [ ] 支持多维度的MRP运算范围选择
- [ ] 运算结果包含物料、需求数量、建议供应商、期望到货日期
- [ ] 100个订单（平均每个BOM 10行）运算时间 < 2分钟
- [ ] 运算准确率 ≥ 99%
- [ ] 支持运算参数的灵活配置
- [ ] 运算过程可视化，显示进度和状态
- [ ] 异常检测和处理机制完善
- [ ] 运算结果支持多维度筛选和排序
- [ ] 支持运算结果的批量操作
- [ ] 运算日志记录完整，支持问题追溯
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 运算启动响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

# 功能模块规格说明书：采购订单管理模块

- **模块ID**: PMS-005
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 采购员, **I want to** 创建和管理采购订单, **so that** 高效执行采购任务。
- **As a** 采购员, **I want to** 跟踪采购订单状态, **so that** 及时了解采购进度和处理异常。
- **As a** 采购经理, **I want to** 审核采购订单, **so that** 确保采购决策的合规性和合理性。
- **As a** 仓库管理员, **I want to** 查看即将到货的订单, **so that** 提前准备收货和入库工作。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 供应商档案已建立
- 物料主数据已维护
- 采购权限已配置
- 审批流程已设置

### 核心流程

#### 2.1 采购订单创建流程
1. 采购员进入订单创建页面
2. 选择订单来源：
   - 从采购建议转换
   - 手动创建新订单
   - 从历史订单复制
3. 填写订单基本信息：
   - 供应商选择
   - 交货地址
   - 期望交期
4. 添加订单明细：
   - 物料选择
   - 采购数量
   - 单价和金额
5. 设置付款条件和备注
6. 保存订单并提交审核

#### 2.2 采购订单审核流程
1. 系统根据订单金额确定审核级别
2. 发送审核通知给相应审核人
3. 审核人查看订单详情和采购理由
4. 审核人做出审核决定：
   - 通过：订单状态变为"已审核"
   - 拒绝：订单状态变为"已拒绝"，返回修改
   - 退回：要求补充信息或修改
5. 审核完成后通知采购员

#### 2.3 采购订单执行流程
1. 已审核订单发送给供应商
2. 供应商确认订单和交期
3. 采购员跟踪订单执行进度
4. 供应商按期发货
5. 仓库接收货物并进行质检
6. 质检合格后办理入库手续
7. 订单状态更新为"已完成"

#### 2.4 采购订单变更流程
1. 采购员或供应商提出变更申请
2. 填写变更内容和原因：
   - 数量变更
   - 交期调整
   - 价格变更
   - 规格修改
3. 系统评估变更影响
4. 提交变更审核
5. 审核通过后更新订单信息
6. 通知相关人员变更结果

### 后置条件
- 采购订单状态正确维护
- 相关人员收到状态通知
- 库存预期正确更新
- 财务应付账款正确记录

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：采购订单管理页面
### 页面目标：提供全面的采购订单管理界面

### 信息架构：
- **顶部区域**：包含 订单概览统计, 快捷操作按钮, 搜索筛选器
- **中间区域**：包含 订单列表表格, 订单详情面板, 状态流程图
- **底部区域**：包含 分页控件, 批量操作, 导出功能

### 交互逻辑与状态：

#### **订单概览统计**
- **统计卡片组：** 5个关键指标卡片
  - **待审核订单：** 橙色数字，显示数量和金额
  - **执行中订单：** 蓝色数字，显示数量和预计到货
  - **逾期订单：** 红色数字，显示逾期数量和天数
  - **本月完成：** 绿色数字，显示完成订单和金额
  - **总采购金额：** 紫色数字，显示当期总金额
- **趋势图表：** 显示订单数量和金额的月度趋势

#### **快捷操作按钮组**
- **新建订单按钮：**
  - **默认状态：** 绿色背景(#52C41A)，白色文字"新建订单"
  - **悬停状态：** 背景色加深至#389E0D
  - **权限不足状态：** 灰色背景，禁用状态
- **从建议创建：** 蓝色边框按钮，从采购建议创建订单
- **批量导入：** 橙色边框按钮，Excel批量导入订单
- **模板下载：** 灰色边框按钮，下载导入模板

#### **搜索筛选器**
- **订单编号搜索：** 文本输入框，支持模糊搜索
- **供应商筛选：** 下拉多选，支持搜索
- **订单状态筛选：** 标签页形式（全部/待审核/执行中/已完成）
- **创建时间：** 日期范围选择器
- **金额范围：** 滑块控件，设置金额区间
- **采购员筛选：** 下拉选择，筛选创建人

#### **订单列表表格**
- **表格样式：** 固定表头，斑马纹行，支持排序
- **列定义：**
  - 选择：复选框，支持批量选择
  - 订单编号：系统生成，链接查看详情
  - 供应商名称：显示供应商，悬停显示联系方式
  - 订单金额：金额显示，大额订单突出显示
  - 创建日期：相对时间显示
  - 期望交期：日期显示，逾期红色标识
  - 订单状态：状态标签
  - 创建人：显示采购员姓名
  - 操作：查看、编辑、打印、发送

#### **订单状态标识**
- **草稿：** 灰色标签(#8C8C8C)，"草稿"
- **待审核：** 橙色标签(#FAAD14)，"待审核"
- **已审核：** 蓝色标签(#1890FF)，"已审核"
- **执行中：** 绿色标签(#52C41A)，"执行中"
- **部分到货：** 紫色标签(#722ED1)，"部分到货"
- **已完成：** 深绿色标签(#389E0D)，"已完成"
- **已取消：** 红色标签(#F5222D)，"已取消"

#### **订单详情面板**
- **基本信息卡片：**
  - 订单编号：大号字体显示
  - 创建时间：详细时间显示
  - 创建人：采购员信息
  - 审核状态：审核流程显示
- **供应商信息卡片：**
  - 供应商名称：突出显示
  - 联系人和电话：可点击拨打
  - 交货地址：完整地址信息
  - 付款条件：账期和方式
- **订单明细表格：**
  - 物料编码：链接查看物料详情
  - 物料名称：显示规格型号
  - 采购数量：数值和单位
  - 单价：金额显示
  - 小计：自动计算
  - 到货数量：实际到货统计
  - 状态：明细状态

#### **订单创建/编辑对话框**
- **供应商选择：** 下拉搜索，显示供应商评级
- **交货地址：** 下拉选择或手动输入
- **期望交期：** 日期选择器，显示供应商交期能力
- **付款条件：** 下拉选择，显示常用条件
- **订单明细表格：**
  - 物料选择：搜索选择器，显示库存信息
  - 数量输入：数值输入框，显示建议数量
  - 单价输入：金额输入框，显示历史价格
  - 添加行：按钮，增加明细行
  - 删除行：图标按钮，删除明细行
- **金额汇总：** 自动计算总金额和税额

#### **订单审核对话框**
- **订单信息展示：** 只读显示订单基本信息
- **明细信息展示：** 表格显示订单明细
- **审核意见：** 文本区域，填写审核意见
- **审核决定：** 单选按钮（通过/拒绝/退回）
- **通过按钮：** 绿色按钮，审核通过
- **拒绝按钮：** 红色边框按钮，审核拒绝
- **退回按钮：** 橙色边框按钮，退回修改

#### **订单状态流程图**
- **流程节点：** 圆形节点显示各个状态
- **当前状态：** 蓝色高亮显示
- **已完成状态：** 绿色显示
- **未到达状态：** 灰色显示
- **连接线：** 显示流程方向
- **时间标注：** 显示各节点的时间

#### **订单变更申请**
- **变更类型：** 单选按钮（数量/交期/价格/规格）
- **变更原因：** 下拉选择常见原因
- **变更详情：** 表格显示变更前后对比
- **影响分析：** 自动分析变更影响
- **申请理由：** 文本区域，详细说明
- **提交申请按钮：** 蓝色按钮，提交变更申请

#### **批量操作工具栏**
- **全选复选框：** 控制列表项的全选/取消全选
- **批量审核：** 绿色按钮，批量审核通过
- **批量发送：** 蓝色边框按钮，批量发送给供应商
- **批量打印：** 灰色边框按钮，批量打印订单
- **批量取消：** 红色边框按钮，批量取消订单

### 数据校验规则：

#### **供应商选择**
- **校验规则：** 必须选择有效且状态正常的供应商
- **错误提示文案：** "请选择有效的供应商"

#### **订单明细**
- **校验规则：** 至少包含一行明细，数量和单价必须大于0
- **错误提示文案：** "订单明细不能为空，数量和单价必须大于0"

#### **期望交期**
- **校验规则：** 不能早于当前日期
- **错误提示文案：** "期望交期不能早于当前日期"

#### **订单金额**
- **校验规则：** 总金额不能为0，超过限额需要审核
- **错误提示文案：** "订单金额不能为0"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **订单基本信息**:
  - **供应商ID (supplier_id)**: String, 必填
  - **交货地址 (delivery_address)**: String, 必填
  - **期望交期 (expected_date)**: Date, 必填
  - **付款条件 (payment_terms)**: String, 可选
  - **备注 (remarks)**: String, 可选, 最大1000字符
- **订单明细**:
  - **物料编码 (material_code)**: String, 必填
  - **采购数量 (quantity)**: Number, 必填, 大于0
  - **单价 (unit_price)**: Number, 必填, 大于0
  - **交期 (delivery_date)**: Date, 可选

### 展示数据
- **订单概览**: 订单数量、状态分布、金额统计
- **订单列表**: 编号、供应商、金额、状态、交期
- **订单详情**: 完整的订单信息和明细
- **审核记录**: 审核人、时间、意见、结果
- **执行进度**: 发货、到货、入库状态

### 空状态/零数据
- **无采购订单**: 显示"暂无采购订单，点击新建开始创建"
- **搜索无结果**: 显示"未找到匹配的订单，请调整搜索条件"
- **无明细数据**: 显示"订单明细为空，请添加采购物料"

### API接口
- **创建订单**: POST /api/purchase-orders
- **获取订单列表**: GET /api/purchase-orders
- **更新订单**: PUT /api/purchase-orders/{id}
- **审核订单**: POST /api/purchase-orders/{id}/approve
- **取消订单**: POST /api/purchase-orders/{id}/cancel

## 5. 异常与边界处理 (Error & Edge Cases)

### **供应商状态异常**
- **提示信息**: "选择的供应商已被禁用，请选择其他供应商"
- **用户操作**: 自动推荐替代供应商，显示供应商状态

### **库存不足预警**
- **提示信息**: "当前库存不足，建议增加采购数量"
- **用户操作**: 显示库存信息，提供数量调整建议

### **价格异常波动**
- **提示信息**: "采购价格与历史价格差异较大，请确认价格准确性"
- **用户操作**: 显示历史价格对比，提供价格确认选项

### **交期冲突**
- **提示信息**: "期望交期与供应商交期能力不符，可能影响交货"
- **用户操作**: 显示供应商交期能力，建议调整交期

### **审核超时**
- **提示信息**: "订单审核超时，已自动提醒审核人"
- **用户操作**: 显示审核进度，提供催办功能

### **订单重复**
- **提示信息**: "检测到相似的采购订单，请确认是否重复下单"
- **用户操作**: 显示相似订单列表，提供对比功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持采购订单的创建、编辑、查询、取消操作
- [ ] 订单信息包含供应商、物料明细、金额、交期等
- [ ] 支持从采购建议快速创建订单
- [ ] 订单审核流程正确执行，支持多级审核
- [ ] 订单状态管理正确（草稿/待审核/执行中/已完成等）
- [ ] 支持订单变更申请和审核流程
- [ ] 订单打印和发送功能正常
- [ ] 支持多维度搜索和筛选功能
- [ ] 批量操作功能正常（审核、发送、打印、取消）
- [ ] 订单数据校验和业务规则检查完善
- [ ] 界面支持响应式设计，移动端可查看
- [ ] 订单列表查询响应时间小于2秒
- [ ] 订单创建保存响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

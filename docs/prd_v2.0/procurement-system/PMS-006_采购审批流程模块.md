# 功能模块规格说明书：采购审批流程模块

- **模块ID**: PMS-006
- **所属子系统**: 采购管理子系统(PMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 采购经理, **I want to** 配置采购审批流程规则, **so that** 确保采购决策的合规性和风险控制。
- **As a** 审批人, **I want to** 高效处理采购审批任务, **so that** 不影响采购进度和业务效率。
- **As a** 采购员, **I want to** 跟踪审批进度, **so that** 及时了解订单状态和处理异常。
- **As a** 财务经理, **I want to** 审核大额采购订单, **so that** 控制采购成本和资金风险。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 组织架构和人员权限已配置
- 审批流程规则已设置
- 采购订单已创建
- 审批人员已分配

### 核心流程

#### 2.1 审批流程配置流程
1. 采购经理进入流程配置页面
2. 设置审批触发条件：
   - 按订单金额分级（如：1万以下/1-10万/10万以上）
   - 按物料类型分类（如：原材料/设备/服务）
   - 按供应商类型分类（如：新供应商/黑名单供应商）
3. 配置审批层级和人员：
   - 一级审批：部门主管
   - 二级审批：采购经理
   - 三级审批：财务经理/总经理
4. 设置审批时限和超时处理
5. 保存流程配置并生效

#### 2.2 审批任务自动分配流程
1. 采购订单提交审批时触发流程
2. 系统根据订单属性匹配审批规则
3. 确定审批层级和审批人员
4. 自动创建审批任务并发送通知
5. 审批人收到待办任务提醒
6. 系统记录审批任务创建日志

#### 2.3 审批任务处理流程
1. 审批人登录系统查看待办任务
2. 点击审批任务查看订单详情
3. 审核订单信息和采购理由：
   - 检查供应商资质和评级
   - 验证采购数量和价格合理性
   - 确认预算和资金可用性
4. 做出审批决定：
   - 同意：审批通过，流转下一级或完成
   - 拒绝：审批拒绝，返回采购员修改
   - 退回：要求补充信息或调整
5. 填写审批意见并提交
6. 系统自动流转到下一审批节点

#### 2.4 审批异常处理流程
1. 系统监控审批任务的处理时效
2. 审批超时时自动发送催办通知
3. 连续超时时启动代理审批机制
4. 审批人请假时可委托他人代理审批
5. 紧急情况下可申请加急审批
6. 异常情况记录完整的处理日志

### 后置条件
- 审批结果正确记录
- 订单状态及时更新
- 相关人员收到结果通知
- 审批日志完整保存

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：采购审批流程管理页面
### 页面目标：提供高效的审批流程配置和处理界面

### 信息架构：
- **顶部区域**：包含 审批概览统计, 快捷操作按钮, 流程配置入口
- **中间区域**：包含 待办任务列表, 审批详情面板, 流程进度图
- **底部区域**：包含 审批历史, 分页控件, 批量操作

### 交互逻辑与状态：

#### **审批概览统计**
- **统计卡片组：** 4个关键指标卡片
  - **待我审批：** 红色数字，显示待处理任务数量
  - **今日已审批：** 绿色数字，显示今日处理数量
  - **超时任务：** 橙色数字，显示超时未处理任务
  - **本月审批：** 蓝色数字，显示本月审批总数
- **审批效率图表：** 显示审批时效和通过率趋势

#### **快捷操作按钮组**
- **流程配置按钮：**
  - **默认状态：** 蓝色背景(#1890FF)，白色文字"流程配置"
  - **权限不足状态：** 灰色背景，禁用状态
- **批量审批：** 绿色边框按钮，批量处理相同类型任务
- **代理设置：** 橙色边框按钮，设置审批代理人
- **审批报告：** 灰色边框按钮，生成审批统计报告

#### **待办任务列表**
- **任务优先级标识：**
  - **紧急：** 红色标签(#F5222D)，"紧急"，闪烁效果
  - **重要：** 橙色标签(#FAAD14)，"重要"
  - **普通：** 绿色标签(#52C41A)，"普通"
- **表格列定义：**
  - 优先级：彩色标签显示
  - 任务编号：链接，点击查看详情
  - 订单编号：链接，查看订单信息
  - 申请人：显示采购员姓名
  - 供应商：显示供应商名称
  - 订单金额：金额显示，大额突出
  - 提交时间：相对时间显示
  - 剩余时间：倒计时显示，超时红色
  - 操作：审批、查看、转办

#### **审批任务状态标识**
- **待审批：** 橙色标签(#FAAD14)，"待审批"
- **审批中：** 蓝色标签(#1890FF)，"审批中"
- **已通过：** 绿色标签(#52C41A)，"已通过"
- **已拒绝：** 红色标签(#F5222D)，"已拒绝"
- **已退回：** 紫色标签(#722ED1)，"已退回"
- **已超时：** 灰色标签(#8C8C8C)，"已超时"

#### **审批详情面板**
- **任务信息卡片：**
  - 任务编号：大号字体显示
  - 提交时间：详细时间显示
  - 当前节点：突出显示当前审批环节
  - 剩余时间：倒计时显示
- **订单信息卡片：**
  - 订单基本信息：只读显示
  - 供应商信息：显示评级和历史合作
  - 采购明细：表格显示物料和金额
  - 采购理由：显示申请原因
- **风险评估卡片：**
  - 供应商风险：评级和风险提示
  - 价格风险：与历史价格对比
  - 预算风险：预算使用情况
  - 合规风险：合规性检查结果

#### **审批决策区域**
- **审批意见输入：** 文本区域，必填项
- **审批决定选择：** 单选按钮组
  - **同意：** 绿色按钮，审批通过
  - **拒绝：** 红色按钮，审批拒绝
  - **退回：** 橙色按钮，退回修改
- **附件上传：** 支持上传审批相关文件
- **提交审批按钮：** 大号绿色按钮，提交审批结果

#### **流程进度图**
- **流程节点：** 圆形节点显示各审批环节
- **当前节点：** 蓝色高亮显示，带动画效果
- **已完成节点：** 绿色显示，带勾选标记
- **未到达节点：** 灰色显示
- **审批人信息：** 显示各节点的审批人
- **时间信息：** 显示各节点的处理时间

#### **流程配置对话框**
- **触发条件设置：**
  - **金额分级：** 表格形式设置金额区间
  - **物料类型：** 多选列表，选择需要审批的类型
  - **供应商类型：** 复选框组，特殊供应商审批
- **审批层级配置：**
  - **层级设置：** 动态添加审批层级
  - **审批人选择：** 下拉选择或组织架构选择
  - **并行审批：** 开关控件，启用并行审批
- **时限设置：**
  - **审批时限：** 数值输入，设置各层级时限
  - **超时处理：** 下拉选择（自动通过/自动拒绝/转办）

#### **代理设置对话框**
- **代理时间：** 日期范围选择器
- **代理人选择：** 下拉搜索，选择代理审批人
- **代理范围：** 复选框组，选择代理的审批类型
- **代理原因：** 文本输入，说明代理原因
- **启用代理按钮：** 绿色按钮，启用代理设置

#### **批量审批工具**
- **任务筛选：** 按类型、金额、供应商筛选任务
- **批量选择：** 复选框，选择相同类型的任务
- **统一意见：** 文本输入，批量审批意见
- **批量决定：** 单选按钮，统一审批决定
- **执行批量审批：** 绿色按钮，执行批量操作

#### **审批历史记录**
- **时间轴显示：** 垂直时间轴显示审批历程
- **审批节点：** 显示审批人、时间、决定、意见
- **状态变更：** 显示状态变更的详细信息
- **操作日志：** 显示所有相关操作记录

### 数据校验规则：

#### **审批意见**
- **校验规则：** 必填，最少10个字符
- **错误提示文案：** "请填写审批意见，至少10个字符"

#### **审批决定**
- **校验规则：** 必须选择一个审批决定
- **错误提示文案：** "请选择审批决定"

#### **流程配置**
- **校验规则：** 金额区间不能重叠，审批人不能为空
- **错误提示文案：** "金额区间不能重叠，请检查配置"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **审批决定**:
  - **审批结果 (approval_result)**: Enum, 必填, [同意/拒绝/退回]
  - **审批意见 (approval_comment)**: String, 必填, 最少10字符
  - **附件文件 (attachments)**: Array, 可选, 文件列表
- **流程配置**:
  - **触发条件 (trigger_conditions)**: Object, 金额/类型/供应商条件
  - **审批层级 (approval_levels)**: Array, 审批人和层级配置
  - **时限设置 (time_limits)**: Object, 各层级时限配置

### 展示数据
- **审批概览**: 待办数量、处理效率、超时统计
- **任务列表**: 任务信息、优先级、剩余时间
- **审批详情**: 订单信息、风险评估、流程进度
- **审批历史**: 审批记录、状态变更、操作日志
- **流程配置**: 当前流程规则和审批人设置

### 空状态/零数据
- **无待办任务**: 显示"暂无待审批任务，工作已处理完毕"
- **无审批历史**: 显示"暂无审批记录"
- **流程未配置**: 显示"请先配置审批流程规则"

### API接口
- **获取待办任务**: GET /api/approval-tasks
- **处理审批任务**: POST /api/approval-tasks/{id}/process
- **配置审批流程**: POST /api/approval-workflows
- **设置审批代理**: POST /api/approval-delegates
- **获取审批历史**: GET /api/approval-history/{order_id}

## 5. 异常与边界处理 (Error & Edge Cases)

### **审批人不在岗**
- **提示信息**: "当前审批人不在岗，已自动转给代理人处理"
- **用户操作**: 显示代理人信息，提供代理设置功能

### **审批超时**
- **提示信息**: "审批任务已超时，根据规则自动处理"
- **用户操作**: 显示超时处理结果，提供催办功能

### **流程配置冲突**
- **提示信息**: "审批流程配置存在冲突，请检查规则设置"
- **用户操作**: 高亮冲突项，提供修复建议

### **并发审批冲突**
- **提示信息**: "任务已被其他人处理，请刷新页面"
- **用户操作**: 自动刷新任务状态，显示最新信息

### **权限不足**
- **提示信息**: "您没有权限审批此类订单"
- **用户操作**: 隐藏审批按钮，显示权限要求

### **系统异常**
- **提示信息**: "审批系统异常，请稍后重试"
- **用户操作**: 提供重试按钮，保存审批草稿

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持灵活的审批流程配置（按金额/类型/供应商）
- [ ] 审批任务自动分配和流转
- [ ] 支持多级审批和并行审批
- [ ] 审批时限控制和超时处理机制
- [ ] 审批代理和委托功能正常
- [ ] 审批决定包含同意、拒绝、退回选项
- [ ] 审批意见必填，支持附件上传
- [ ] 审批进度可视化显示
- [ ] 支持批量审批功能
- [ ] 审批历史记录完整，支持审计追溯
- [ ] 界面支持响应式设计，移动端可审批
- [ ] 审批任务查询响应时间小于2秒
- [ ] 审批提交响应时间小于3秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

# 功能模块规格说明书：成本要素归集模块

- **模块ID**: FMS-009
- **所属子系统**: 财务管理子系统(FMS)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 成本会计, **I want to** 自动归集直接材料成本, **so that** 准确计算产品成本。
- **As a** 成本会计, **I want to** 分摊间接费用, **so that** 合理分配制造费用到各产品。
- **As a** 财务主管, **I want to** 分析成本构成, **so that** 优化成本结构和控制成本。
- **As a** 生产经理, **I want to** 查看实时成本信息, **so that** 及时调整生产计划。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 生产订单已下达
- 物料消耗已记录
- 工时数据已采集
- 费用分摊规则已设置

### 核心流程

#### 2.1 直接材料成本归集流程
1. 系统自动获取生产领料单据
2. 按生产订单汇总材料消耗
3. 根据材料出库成本计算材料费用
4. 处理材料损耗和报废成本
5. 生成直接材料成本明细
6. 更新生产订单成本数据

#### 2.2 直接人工成本归集流程
1. 获取生产工时记录数据
2. 按工序和工人汇总工时
3. 根据工资标准计算人工费用
4. 处理加班费和津贴补贴
5. 分配人工成本到生产订单
6. 生成直接人工成本明细

#### 2.3 制造费用归集流程
1. 收集各类制造费用发生额
2. 按费用性质进行分类汇总
3. 设置费用分摊基础和比例
4. 计算各产品应分摊费用
5. 执行制造费用分摊
6. 生成制造费用分摊明细

#### 2.4 在产品成本计算流程
1. 统计期末在产品数量
2. 确定在产品完工程度
3. 计算约当产量
4. 分配成本到完工产品和在产品
5. 更新在产品成本
6. 生成在产品成本报表

#### 2.5 成本差异分析流程
1. 对比实际成本与标准成本
2. 分析材料、人工、费用差异
3. 识别差异产生原因
4. 计算差异影响金额
5. 生成成本差异分析报告
6. 提出成本改进建议

### 后置条件
- 成本要素数据准确归集
- 生产订单成本及时更新
- 成本分摊关系清晰
- 成本分析报告生成

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：成本要素归集管理页面
### 页面目标：提供完整的成本要素归集和分摊功能

### 信息架构：
- **顶部区域**：包含 成本查询, 成本归集, 费用分摊, 差异分析
- **左侧区域**：包含 期间选择, 产品筛选, 成本要素筛选
- **中间区域**：包含 成本明细列表, 归集界面
- **右侧区域**：包含 成本统计, 分摊规则, 差异分析

### 交互逻辑与状态：

#### **成本归集设置区域**
- **基础设置：**
  - **归集期间：** 日期范围选择器，支持月度/季度
  - **产品范围：** 多选下拉，选择要归集的产品
  - **成本要素：** 复选框组，直接材料/直接人工/制造费用
  - **归集方式：** 单选按钮，自动归集/手工归集
- **高级设置：**
  - **数据来源：** 下拉选择，生产系统/仓储系统/人事系统
  - **归集精度：** 下拉选择，订单级/批次级/工序级
  - **差异处理：** 下拉选择，自动调整/手工处理
  - **分摊基础：** 下拉选择，工时/机时/产量/金额

#### **直接材料成本区域**
- **材料消耗明细：**
  - **生产订单：** 显示生产订单编号
  - **物料编码：** 显示消耗物料编码
  - **物料名称：** 显示物料名称规格
  - **消耗数量：** 显示实际消耗数量
  - **单位成本：** 显示材料单位成本
  - **材料成本：** 计算得出的材料成本
- **成本汇总：**
  - **主要材料：** 汇总主要原材料成本
  - **辅助材料：** 汇总辅助材料成本
  - **包装材料：** 汇总包装材料成本
  - **材料损耗：** 汇总材料损耗成本
- **异常处理：**
  - **超耗材料：** 标红显示超定额消耗
  - **负消耗：** 标蓝显示退料情况
  - **无成本：** 标黄显示无成本价材料
  - **处理建议：** 显示异常处理建议

#### **直接人工成本区域**
- **工时记录明细：**
  - **生产订单：** 显示生产订单编号
  - **工序名称：** 显示生产工序名称
  - **工人姓名：** 显示操作工人姓名
  - **工作时间：** 显示实际工作时间
  - **小时工资：** 显示工人小时工资
  - **人工成本：** 计算得出的人工成本
- **成本分类：**
  - **基本工资：** 基本工资部分成本
  - **加班工资：** 加班工资部分成本
  - **津贴补贴：** 各类津贴补贴成本
  - **社会保险：** 社保费用分摊成本
- **效率分析：**
  - **标准工时：** 显示标准工时定额
  - **实际工时：** 显示实际消耗工时
  - **工时差异：** 计算工时差异
  - **效率指标：** 计算工时效率

#### **制造费用分摊区域**
- **费用收集：**
  - **设备折旧：** 生产设备折旧费用
  - **水电费：** 生产用水电费用
  - **维修费：** 设备维修保养费用
  - **其他费用：** 其他制造费用
- **分摊设置：**
  - **分摊基础：** 选择分摊基础（工时/机时/产量）
  - **分摊比例：** 设置各产品分摊比例
  - **分摊规则：** 配置分摊计算规则
  - **分摊周期：** 设置分摊计算周期
- **分摊结果：**
  - **产品编码：** 显示产品编码
  - **分摊基数：** 显示分摊基础数据
  - **分摊比例：** 显示分摊比例
  - **分摊金额：** 计算得出的分摊金额

#### **在产品成本计算**
- **在产品统计：**
  - **产品编码：** 在产品编码
  - **在产数量：** 期末在产品数量
  - **完工程度：** 在产品完工程度
  - **约当产量：** 计算的约当产量
- **成本分配：**
  - **完工产品：** 完工产品应负担成本
  - **在产品：** 在产品应负担成本
  - **分配比例：** 成本分配比例
  - **分配金额：** 具体分配金额
- **成本结转：**
  - **完工入库：** 完工产品入库成本
  - **在产结存：** 在产品结存成本
  - **成本差异：** 成本计算差异
  - **调整分录：** 成本调整会计分录

#### **成本差异分析**
- **差异类型：**
  - **材料差异：** 材料价格差异和用量差异
  - **人工差异：** 人工效率差异和工资差异
  - **费用差异：** 制造费用预算差异和效率差异
  - **总差异：** 总成本差异汇总
- **差异明细：**
  - **标准成本：** 显示标准成本金额
  - **实际成本：** 显示实际发生成本
  - **差异金额：** 计算差异金额
  - **差异率：** 计算差异百分比
- **差异分析：**
  - **有利差异：** 绿色显示有利差异
  - **不利差异：** 红色显示不利差异
  - **差异原因：** 分析差异产生原因
  - **改进建议：** 提出成本改进建议

#### **成本报表展示**
- **成本汇总表：**
  - **产品成本：** 各产品总成本
  - **单位成本：** 产品单位成本
  - **成本构成：** 成本要素构成比例
  - **环比变化：** 与上期成本对比
- **成本明细表：**
  - **材料明细：** 详细材料成本明细
  - **人工明细：** 详细人工成本明细
  - **费用明细：** 详细制造费用明细
  - **分摊明细：** 费用分摊详细过程
- **图表分析：**
  - **成本趋势：** 成本变化趋势图
  - **构成分析：** 成本构成饼图
  - **对比分析：** 产品成本对比图
  - **差异分析：** 成本差异分析图

#### **批量操作功能**
- **批量归集：**
  - **批量选择：** 选择多个生产订单
  - **批量归集：** 批量执行成本归集
  - **进度显示：** 显示归集进度
  - **结果统计：** 显示归集结果统计
- **批量分摊：**
  - **批量设置：** 批量设置分摊规则
  - **批量计算：** 批量计算费用分摊
  - **批量确认：** 批量确认分摊结果
  - **批量调整：** 批量调整分摊金额

### 数据校验规则：

#### **归集期间**
- **校验规则：** 期间不能为空，不能选择未来期间
- **错误提示文案：** "请选择有效的归集期间"

#### **分摊比例**
- **校验规则：** 分摊比例总和必须等于100%
- **错误提示文案：** "分摊比例总和必须等于100%"

#### **成本金额**
- **校验规则：** 成本金额必须大于等于0
- **错误提示文案：** "成本金额不能为负数"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **归集设置**:
  - **归集期间 (period)**: DateRange, 必填, 成本归集期间
  - **产品范围 (product_range)**: String[], 必填, 归集产品范围
  - **成本要素 (cost_elements)**: String[], 必填, 归集成本要素
  - **分摊基础 (allocation_base)**: String, 必填, 费用分摊基础
- **分摊规则**:
  - **分摊方式 (allocation_method)**: Enum, 必填, 分摊方式
  - **分摊比例 (allocation_ratio)**: Decimal, 必填, 分摊比例

### 展示数据
- **成本明细**: 材料、人工、费用成本明细
- **分摊结果**: 制造费用分摊结果
- **差异分析**: 标准成本与实际成本差异
- **成本报表**: 各类成本分析报表

### 空状态/零数据
- **无成本数据**: 显示"该期间无成本数据"
- **无生产订单**: 显示"该期间无生产订单"
- **无费用发生**: 显示"该期间无制造费用发生"

### API接口
- **成本归集**: POST /api/cost/collect
- **费用分摊**: POST /api/cost/allocate
- **差异分析**: GET /api/cost/variance
- **成本查询**: GET /api/cost/details
- **报表生成**: GET /api/cost/reports

## 5. 异常与边界处理 (Error & Edge Cases)

### **数据缺失**
- **提示信息**: "生产数据不完整，无法进行成本归集"
- **用户操作**: 显示缺失数据明细和补充建议

### **分摊基础为零**
- **提示信息**: "分摊基础数据为零，无法进行费用分摊"
- **用户操作**: 提供分摊基础调整和替代方案

### **成本异常**
- **提示信息**: "检测到异常成本数据，请核实"
- **用户操作**: 显示异常数据明细和处理建议

### **计算错误**
- **提示信息**: "成本计算出现错误，请重新计算"
- **用户操作**: 提供重新计算和数据修复选项

### **权限限制**
- **提示信息**: "您没有权限查看该产品的成本信息"
- **用户操作**: 显示权限要求和申请流程

## 6. 验收标准 (Acceptance Criteria)

- [ ] 直接材料成本自动准确归集
- [ ] 直接人工成本按工时正确分配
- [ ] 制造费用按设定规则合理分摊
- [ ] 在产品成本计算准确
- [ ] 成本差异分析清晰明确
- [ ] 支持多种分摊基础和方法
- [ ] 成本归集响应时间<10秒
- [ ] 成本数据与生产数据100%一致
- [ ] 支持批量成本归集操作
- [ ] 所有页面元素符合全局设计规范
- [ ] 成本报表数据准确完整
- [ ] 异常成本数据及时预警

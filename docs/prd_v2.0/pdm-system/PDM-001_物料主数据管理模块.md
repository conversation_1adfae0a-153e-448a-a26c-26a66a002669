# 功能模块规格说明书：物料主数据管理模块

- **模块ID**: PDM-001
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 维护统一的物料主数据库, **so that** 为BOM设计提供标准化的物料信息。
- **As a** 工艺工程师, **I want to** 对物料进行分类管理, **so that** 快速查找和选择所需物料。
- **As a** 工艺工程师, **I want to** 管理玻璃类物料的特殊属性, **so that** 支持玻璃深加工行业的专业需求。
- **As a** 系统管理员, **I want to** 控制物料编码规则, **so that** 确保物料编码的规范性和唯一性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有物料管理权限
- 物料分类体系已建立
- 物料编码规则已配置

### 核心流程

#### 2.1 物料创建流程
1. 工艺工程师点击"新增物料"按钮
2. 选择物料类型（原材料/半成品/成品/辅料）
3. 系统根据类型自动生成物料编码
4. 填写物料基本信息（名称、规格、单位等）
5. 如为玻璃类物料，填写特殊属性（长、宽、厚度、颜色、品级）
6. 选择物料分类和供应商信息
7. 保存物料信息并生成审计日志

#### 2.2 物料分类管理流程
1. 系统管理员进入分类管理界面
2. 创建或编辑物料分类节点
3. 设置分类层级关系
4. 配置分类权限控制
5. 保存分类结构并同步到物料选择器

#### 2.3 物料查询和筛选流程
1. 用户进入物料管理页面
2. 通过分类导航或搜索框查找物料
3. 应用筛选条件（类型、状态、供应商等）
4. 查看物料详细信息
5. 执行编辑、禁用等操作

### 后置条件
- 物料信息更新立即生效
- 相关BOM和工艺路线自动同步
- 所有操作记录到审计日志

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：物料主数据管理页
### 页面目标：提供高效的物料信息管理和查询界面

### 信息架构：
- **左侧区域**：包含 物料分类树, 分类搜索, 新增分类按钮
- **中间区域**：包含 物料列表, 搜索筛选工具栏, 批量操作工具
- **右侧区域**：包含 物料详情面板, 编辑表单, 关联信息展示

### 交互逻辑与状态：

#### **物料分类树**
- **默认状态：** 展示根分类，子分类折叠显示
- **展开状态：** 点击分类前的展开图标，显示子分类
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中分类，中间区域显示对应物料列表

#### **新增物料按钮**
- **默认状态：** 蓝色背景(#1890FF)，白色文字"新增物料"
- **悬停状态：** 背景色加深至#096DD9
- **权限不足状态：** 灰色背景，禁用状态
- **交互行为：** 点击打开新增物料对话框

#### **物料搜索框**
- **默认状态：** 占位符"搜索物料编码、名称或规格"，搜索图标
- **聚焦状态：** 蓝色边框，显示搜索历史下拉
- **搜索中状态：** 显示加载图标，实时搜索
- **交互行为：** 支持模糊搜索，Enter键确认搜索

#### **物料类型筛选器**
- **默认状态：** 显示"全部类型"，下拉箭头
- **展开状态：** 显示类型选项列表（全部/原材料/半成品/成品/辅料）
- **选中状态：** 显示选中的类型，蓝色文字
- **交互行为：** 点击切换筛选条件，自动刷新列表

#### **物料列表表格**
- **表头样式：** 灰色背景(#FAFAFA)，加粗文字，支持排序
- **行样式：** 奇偶行背景色区分，悬停高亮
- **状态列显示：**
  - 启用：绿色标签(#52C41A)
  - 禁用：灰色标签(#8C8C8C)
- **操作列：** 编辑、查看、禁用/启用、删除按钮

#### **物料详情面板**
- **默认状态：** 右侧滑出面板，显示物料详细信息
- **编辑状态：** 表单字段可编辑，显示保存/取消按钮
- **玻璃类物料：** 显示特殊属性区域（尺寸、颜色、品级）
- **关联信息：** 显示BOM使用情况、供应商信息

#### **物料编码生成器**
- **自动生成：** 根据物料类型和编码规则自动生成
- **手动输入：** 支持手动修改，实时校验唯一性
- **格式验证：** 不符合规则时红色边框提示
- **交互行为：** 失焦时验证编码唯一性

### 数据校验规则：

#### **物料编码**
- **校验规则：** 必填，符合编码规则，全局唯一
- **错误提示文案：** "物料编码不能为空" / "物料编码已存在"

#### **物料名称**
- **校验规则：** 必填，2-100位字符
- **错误提示文案：** "物料名称不能为空"

#### **玻璃尺寸属性**
- **校验规则：** 数值类型，大于0
- **错误提示文案：** "请输入有效的尺寸数值"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **物料编码 (material_code)**: String, 必填, 符合编码规则
- **物料名称 (material_name)**: String, 必填, 2-100位字符
- **物料类型 (material_type)**: Enum, 必填, [原材料/半成品/成品/辅料]
- **规格型号 (specification)**: String, 可选, 最大200字符
- **计量单位 (unit)**: String, 必填, 如"片"、"米"、"公斤"
- **物料分类 (category_id)**: String, 必填, 分类ID
- **玻璃特殊属性**:
  - **长度 (length)**: Number, 可选, 单位毫米
  - **宽度 (width)**: Number, 可选, 单位毫米
  - **厚度 (thickness)**: Number, 可选, 单位毫米
  - **颜色 (color)**: String, 可选
  - **品级 (grade)**: Enum, 可选, [A级/B级/C级]

### 展示数据
- **物料基本信息**: 编码、名称、类型、规格、单位、状态
- **分类信息**: 所属分类路径、分类名称
- **供应商信息**: 主供应商、备选供应商列表
- **使用统计**: BOM使用次数、最近使用时间
- **审计信息**: 创建时间、创建人、最后修改时间、修改人

### 空状态/零数据
- **无物料数据**: 显示"暂无物料数据，请先添加物料"
- **搜索无结果**: 显示"未找到匹配的物料，请尝试其他关键词"
- **分类无物料**: 显示"该分类下暂无物料"

### API接口
- **获取物料列表**: GET /api/materials
- **创建物料**: POST /api/materials
- **更新物料**: PUT /api/materials/{id}
- **删除物料**: DELETE /api/materials/{id}
- **获取分类树**: GET /api/material-categories/tree
- **物料编码生成**: POST /api/materials/generate-code

## 5. 异常与边界处理 (Error & Edge Cases)

### **物料编码重复**
- **提示信息**: "物料编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **删除被BOM引用的物料**
- **提示信息**: "该物料正在被BOM使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"禁用物料"选项

### **分类删除检查**
- **提示信息**: "该分类下还有物料，请先转移后再删除"
- **用户操作**: 显示子物料列表，提供批量转移功能

### **玻璃尺寸数据异常**
- **提示信息**: "玻璃尺寸数据不合理，请检查输入"
- **用户操作**: 异常字段标红，提供合理范围提示

### **网络异常导致保存失败**
- **提示信息**: "保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

### **批量操作部分失败**
- **提示信息**: "批量操作完成，X项成功，Y项失败"
- **用户操作**: 显示失败详情列表，支持重试失败项

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除、查询物料信息
- [ ] 物料编码全局唯一性校验正确执行
- [ ] 支持原材料、半成品、成品、辅料等全类型物料管理
- [ ] 玻璃类物料特殊属性（长、宽、厚度、颜色、品级）正确管理
- [ ] 物料分类树状结构正常工作，支持多级分类
- [ ] 物料搜索功能支持编码、名称、规格模糊查找
- [ ] 筛选功能支持类型、状态、分类多维度筛选
- [ ] 被BOM引用的物料限制删除，提供合理的替代方案
- [ ] 物料编码自动生成规则正确执行
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 物料查询响应时间小于500ms（万级数据）
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问

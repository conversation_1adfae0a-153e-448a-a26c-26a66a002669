# 功能模块规格说明书：参数化BOM设计模块

- **模块ID**: PDM-002
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 创建参数化BOM模板, **so that** 支持复杂产品的快速配置和报价。
- **As a** 工艺工程师, **I want to** 定义参数变量和计算公式, **so that** 实现物料用量的自动计算。
- **As a** 工艺工程师, **I want to** 可视化设计BOM结构, **so that** 直观管理产品的组成关系。
- **As a** 销售代表, **I want to** 预览参数化BOM的计算结果, **so that** 验证配置的正确性。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有BOM设计权限
- 物料主数据已建立
- 成品物料已创建

### 核心流程

#### 2.1 参数化BOM创建流程
1. 工艺工程师选择成品物料
2. 点击"新建参数化BOM"按钮
3. 定义参数变量（如长度L、宽度W、厚度T）
4. 设置变量类型、取值范围和默认值
5. 构建BOM树状结构
6. 为每个BOM行添加物料和用量公式
7. 测试公式计算结果
8. 保存BOM模板

#### 2.2 BOM结构设计流程
1. 在BOM设计器中添加子物料
2. 设置物料层级关系
3. 配置物料用量公式（支持参数变量）
4. 设置物料替代关系
5. 添加工艺说明和备注
6. 预览BOM展开结构

#### 2.3 公式配置和测试流程
1. 选择BOM行物料
2. 在公式编辑器中输入用量计算公式
3. 系统实时进行语法校验
4. 输入测试参数值
5. 查看公式计算结果
6. 调整公式直到满足要求

### 后置条件
- 参数化BOM模板保存成功
- 公式计算结果准确无误
- BOM数据可供销售系统调用

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：参数化BOM设计器
### 页面目标：提供专业的BOM结构设计和参数化配置界面

### 信息架构：
- **左侧区域**：包含 物料选择器, 物料分类树, 物料搜索框
- **中间区域**：包含 BOM树状结构, 拖拽操作区域, 层级展示
- **右侧区域**：包含 参数配置面板, 公式编辑器, 计算测试工具
- **底部区域**：包含 保存按钮, 预览按钮, 测试按钮, 重置按钮

### 交互逻辑与状态：

#### **BOM树状结构**
- **默认状态：** 显示成品物料为根节点，子物料可展开
- **展开状态：** 点击节点前的展开图标，显示下级物料
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **编辑状态：** 双击节点进入编辑模式，显示输入框
- **拖拽状态：** 支持物料节点拖拽调整层级关系
- **交互行为：** 选中节点后右侧显示详细配置

#### **参数变量定义区域**
- **变量列表：** 显示已定义的参数变量（L、W、T等）
- **新增变量按钮：** 蓝色背景，点击打开变量定义对话框
- **变量编辑：** 支持修改变量名称、类型、范围、默认值
- **变量删除：** 删除前检查是否被公式引用

#### **公式编辑器**
- **默认状态：** 显示当前物料的用量公式
- **编辑状态：** 代码编辑器样式，支持语法高亮
- **语法校验：** 实时检查公式语法，错误处红色下划线
- **智能提示：** 输入时显示可用变量和函数列表
- **交互行为：** 支持快捷键操作，Ctrl+S保存

#### **物料选择器**
- **分类导航：** 左侧树状分类结构
- **搜索功能：** 顶部搜索框，支持模糊查找
- **物料列表：** 右侧物料列表，支持拖拽到BOM树
- **筛选器：** 按类型、状态筛选物料

#### **计算测试工具**
- **参数输入区：** 为每个变量提供输入框
- **计算按钮：** 蓝色背景，白色文字"计算测试"
- **结果展示：** 表格形式显示每个物料的计算用量
- **错误提示：** 计算失败时显示具体错误信息

#### **BOM预览面板**
- **树状展示：** 完整的BOM层级结构
- **用量显示：** 每个物料的计算用量和单位
- **成本预估：** 基于物料单价的成本计算
- **导出功能：** 支持导出为Excel格式

### 数据校验规则：

#### **参数变量名称**
- **校验规则：** 必填，字母开头，不能包含特殊字符
- **错误提示文案：** "变量名称格式不正确"

#### **用量公式**
- **校验规则：** 语法正确，引用的变量必须已定义
- **错误提示文案：** "公式语法错误" / "引用了未定义的变量"

#### **物料层级**
- **校验规则：** 不能形成循环引用
- **错误提示文案：** "检测到循环引用，请调整BOM结构"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **BOM编码 (bom_code)**: String, 必填, 自动生成
- **成品物料 (product_material_id)**: String, 必填, 物料ID
- **参数变量定义**:
  - **变量名 (variable_name)**: String, 必填, 如"L"、"W"、"T"
  - **变量类型 (variable_type)**: Enum, 必填, [数值/文本/选择]
  - **取值范围 (value_range)**: String, 可选, 如"100-3000"
  - **默认值 (default_value)**: String, 可选
- **BOM行数据**:
  - **物料ID (material_id)**: String, 必填
  - **用量公式 (quantity_formula)**: String, 必填, 如"L*W/1000000"
  - **层级 (level)**: Integer, 必填, 从1开始
  - **父级ID (parent_id)**: String, 可选

### 展示数据
- **BOM基本信息**: 编码、名称、版本、状态、创建时间
- **参数变量列表**: 变量名、类型、范围、默认值
- **BOM树状结构**: 物料层级关系、用量公式、计算结果
- **公式测试结果**: 各物料计算用量、总成本预估
- **版本历史**: 历史版本列表、变更记录

### 空状态/零数据
- **新建BOM**: 显示"请添加子物料构建BOM结构"
- **无参数变量**: 显示"请先定义参数变量"
- **计算无结果**: 显示"请输入参数值进行计算测试"

### API接口
- **创建BOM**: POST /api/boms
- **更新BOM**: PUT /api/boms/{id}
- **获取BOM详情**: GET /api/boms/{id}
- **公式计算**: POST /api/boms/{id}/calculate
- **BOM预览**: GET /api/boms/{id}/preview
- **公式语法校验**: POST /api/boms/validate-formula

## 5. 异常与边界处理 (Error & Edge Cases)

### **公式语法错误**
- **提示信息**: "公式语法错误：具体错误位置和原因"
- **用户操作**: 错误位置高亮显示，提供修复建议

### **参数变量未定义**
- **提示信息**: "公式中引用了未定义的变量：{变量名}"
- **用户操作**: 高亮未定义变量，提供快速定义选项

### **循环引用检测**
- **提示信息**: "检测到BOM循环引用，请调整结构"
- **用户操作**: 高亮循环引用路径，阻止保存操作

### **计算结果异常**
- **提示信息**: "计算结果异常，请检查公式和参数"
- **用户操作**: 显示异常的物料和公式，提供调试信息

### **物料数据缺失**
- **提示信息**: "物料信息不完整，无法进行计算"
- **用户操作**: 高亮缺失信息的物料，提供补充入口

### **保存失败**
- **提示信息**: "BOM保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除参数化BOM模板
- [ ] 支持定义多个参数变量（长度、宽度、厚度等）
- [ ] 物料用量公式支持四则运算和常用函数
- [ ] 公式语法错误实时提示和校验
- [ ] BOM树状结构支持拖拽调整层级关系
- [ ] 参数化BOM可预览和测试计算结果
- [ ] 公式计算结果精度满足业务要求（≥99.9%）
- [ ] 支持BOM结构的循环引用检测
- [ ] 物料选择器支持分类导航和搜索过滤
- [ ] BOM设计器界面专业易用，符合工程师习惯
- [ ] 参数化BOM计算响应时间小于1秒（10个变量内）
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和快捷键操作

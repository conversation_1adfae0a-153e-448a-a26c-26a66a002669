# 功能模块规格说明书：工艺路线设计模块

- **模块ID**: PDM-005
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 设计标准化的工艺路线, **so that** 为生产提供规范的工艺流程。
- **As a** 工艺工程师, **I want to** 可视化设计工艺流程, **so that** 直观展示工序间的逻辑关系。
- **As a** 工艺工程师, **I want to** 配置工序的详细信息, **so that** 支持精确的生产计划和成本核算。
- **As a** 生产计划员, **I want to** 查看工艺路线的工时信息, **so that** 制定合理的生产排程。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工艺路线设计权限
- 工作中心主数据已建立
- 工序标准已定义

### 核心流程

#### 2.1 工艺路线创建流程
1. 工艺工程师选择产品物料
2. 点击"新建工艺路线"按钮
3. 设置路线基本信息（名称、版本、适用范围）
4. 在流程设计器中添加工序节点
5. 配置工序间的连接关系
6. 设置工序详细信息
7. 验证工艺路线的完整性
8. 保存工艺路线模板

#### 2.2 工序配置流程
1. 在工艺路线中选择工序节点
2. 设置工序基本信息（工序号、名称、描述）
3. 选择工作中心和设备资源
4. 配置工序工时（准备工时、单件工时、拆卸工时）
5. 设置工序参数和质量要求
6. 添加工序说明和注意事项
7. 保存工序配置

#### 2.3 并行工序设计流程
1. 识别可并行执行的工序
2. 在流程设计器中创建并行分支
3. 设置分支条件和汇聚点
4. 配置并行工序的资源需求
5. 验证并行逻辑的正确性
6. 测试并行路径的可行性

### 后置条件
- 工艺路线数据完整准确
- 工序逻辑关系清晰明确
- 工艺路线可供生产系统调用

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工艺路线设计器
### 页面目标：提供专业的工艺流程设计和配置界面

### 信息架构：
- **左侧区域**：包含 工序模板库, 工作中心列表, 组件工具箱
- **中间区域**：包含 流程设计画布, 工序节点, 连接线
- **右侧区域**：包含 工序属性面板, 参数配置, 预览工具
- **底部区域**：包含 缩放控制, 保存按钮, 验证工具

### 交互逻辑与状态：

#### **流程设计画布**
- **默认状态：** 空白画布，显示网格背景
- **拖拽状态：** 支持从工具箱拖拽工序到画布
- **选中状态：** 工序节点蓝色边框，显示操作手柄
- **连接状态：** 鼠标悬停显示连接点，支持拖拽连线
- **缩放状态：** 支持鼠标滚轮缩放，显示缩放比例

#### **工序节点**
- **默认样式：** 圆角矩形，蓝色边框，白色背景
- **选中样式：** 蓝色背景(#E6F7FF)，加粗边框
- **并行节点：** 菱形形状，橙色边框
- **开始/结束节点：** 圆形形状，绿色/红色填充
- **节点内容：** 显示工序号、工序名称、工作中心

#### **工序模板库**
- **分类导航：** 按工序类型分类（切割/磨边/钢化/合片）
- **模板列表：** 显示预定义的工序模板
- **搜索功能：** 支持按工序名称搜索
- **拖拽操作：** 支持拖拽模板到设计画布

#### **工序属性面板**
- **基本信息区：** 工序号、名称、描述输入框
- **工作中心选择：** 下拉选择器，显示可用工作中心
- **工时配置区：** 准备工时、单件工时、拆卸工时输入
- **参数设置区：** 工序特定参数的配置界面
- **质量要求区：** 质量标准和检验要求

#### **连接线工具**
- **直线连接：** 默认连接方式，直线箭头
- **条件分支：** 菱形决策节点，多条输出线
- **并行分支：** 并行网关，支持同时执行
- **汇聚节点：** 汇聚网关，等待所有分支完成

#### **工艺路线验证器**
- **完整性检查：** 验证路线是否有开始和结束节点
- **逻辑检查：** 验证工序连接的逻辑正确性
- **资源检查：** 验证工作中心和设备的可用性
- **工时检查：** 验证工时配置的合理性

#### **预览和导出工具**
- **路线预览：** 以流程图形式预览完整路线
- **工时统计：** 计算总工时和关键路径
- **导出功能：** 支持导出为PDF或图片格式
- **打印功能：** 支持工艺路线的打印输出

### 数据校验规则：

#### **工序号**
- **校验规则：** 必填，数字格式，路线内唯一
- **错误提示文案：** "工序号不能为空且必须唯一"

#### **工序名称**
- **校验规则：** 必填，2-50位字符
- **错误提示文案：** "工序名称不能为空"

#### **工时配置**
- **校验规则：** 数值类型，大于等于0
- **错误提示文案：** "工时必须为非负数"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **路线编码 (route_code)**: String, 必填, 自动生成
- **路线名称 (route_name)**: String, 必填, 2-100位字符
- **适用物料 (material_id)**: String, 必填, 物料ID
- **工序信息**:
  - **工序号 (operation_number)**: Integer, 必填, 如10、20、30
  - **工序名称 (operation_name)**: String, 必填
  - **工作中心 (work_center_id)**: String, 必填
  - **准备工时 (setup_time)**: Number, 可选, 单位分钟
  - **单件工时 (unit_time)**: Number, 必填, 单位分钟
  - **拆卸工时 (teardown_time)**: Number, 可选, 单位分钟

### 展示数据
- **路线基本信息**: 编码、名称、版本、状态、适用物料
- **工序列表**: 工序号、名称、工作中心、工时信息
- **流程图**: 可视化的工艺流程图
- **工时统计**: 总工时、关键路径、瓶颈工序
- **版本历史**: 历史版本和变更记录

### 空状态/零数据
- **新建路线**: 显示"请添加工序构建工艺路线"
- **无工序数据**: 显示"该路线暂无工序，请先添加工序"
- **无历史版本**: 显示"暂无历史版本"

### API接口
- **创建工艺路线**: POST /api/routes
- **更新工艺路线**: PUT /api/routes/{id}
- **获取路线详情**: GET /api/routes/{id}
- **工艺路线验证**: POST /api/routes/{id}/validate
- **导出路线图**: GET /api/routes/{id}/export

## 5. 异常与边界处理 (Error & Edge Cases)

### **工序逻辑错误**
- **提示信息**: "检测到工序逻辑错误：存在循环依赖"
- **用户操作**: 高亮错误的连接线，提供修复建议

### **工作中心冲突**
- **提示信息**: "工作中心资源冲突，请调整工序安排"
- **用户操作**: 显示冲突的工序，提供替代工作中心

### **并行工序配置错误**
- **提示信息**: "并行工序配置错误：缺少汇聚节点"
- **用户操作**: 高亮错误的并行分支，提供修复工具

### **工时配置异常**
- **提示信息**: "工时配置异常：单件工时不能为0"
- **用户操作**: 错误字段标红，聚焦到工时输入框

### **保存失败**
- **提示信息**: "工艺路线保存失败，请检查网络后重试"
- **用户操作**: 保持编辑状态，提供重试按钮

### **导出异常**
- **提示信息**: "工艺路线导出失败，请稍后重试"
- **用户操作**: 提供重新导出选项，记录错误日志

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以创建、编辑、删除工艺路线
- [ ] 支持可视化的工艺流程设计，拖拽操作流畅
- [ ] 工序配置完整：工序号、名称、工作中心、工时
- [ ] 支持并行工序和条件分支的设计
- [ ] 工艺路线逻辑验证正确，防止循环依赖
- [ ] 工序模板库功能正常，支持快速添加常用工序
- [ ] 工时统计准确，支持关键路径分析
- [ ] 工艺路线可视化展示清晰，支持缩放和导航
- [ ] 支持工艺路线的导出和打印功能
- [ ] 工作中心资源冲突检测正常工作
- [ ] 工艺路线版本管理正确
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 工艺路线加载响应时间小于2秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和快捷键操作

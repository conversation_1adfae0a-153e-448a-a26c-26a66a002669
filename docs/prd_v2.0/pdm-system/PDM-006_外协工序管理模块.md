# 功能模块规格说明书：外协工序管理模块

- **模块ID**: PDM-006
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-30

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 标识工序的外协属性, **so that** 区分内制和外协的生产方式。
- **As a** 工艺工程师, **I want to** 管理外协供应商信息, **so that** 为外协工序选择合适的供应商。
- **As a** 采购员, **I want to** 获取外协工序的成本信息, **so that** 进行外协采购和成本控制。
- **As a** 生产计划员, **I want to** 了解外协工序的交期, **so that** 制定合理的生产计划。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有外协工序管理权限
- 工艺路线已创建
- 外协供应商主数据已建立

### 核心流程

#### 2.1 外协工序标识流程
1. 工艺工程师在工艺路线中选择工序
2. 设置工序为"外协"属性
3. 选择外协类型（完全外协/部分外协）
4. 配置外协工序的技术要求
5. 设置外协工序的质量标准
6. 保存外协工序配置

#### 2.2 外协供应商关联流程
1. 为外协工序选择合格供应商
2. 设置主供应商和备选供应商
3. 配置供应商的工序能力信息
4. 设置外协价格和交期
5. 建立供应商评价体系
6. 定期更新供应商信息

#### 2.3 内制外协切换流程
1. 根据生产负荷和成本考虑
2. 评估内制和外协的优劣
3. 制定切换决策和时间计划
4. 更新工艺路线的工序属性
5. 通知相关部门和供应商
6. 跟踪切换效果和成本变化

### 后置条件
- 外协工序信息准确完整
- 供应商关联关系明确
- 外协成本数据及时更新

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：外协工序管理页
### 页面目标：提供高效的外协工序配置和供应商管理界面

### 信息架构：
- **左侧区域**：包含 工艺路线树, 工序筛选器, 外协统计
- **中间区域**：包含 工序列表, 外协标识, 操作工具栏
- **右侧区域**：包含 外协配置面板, 供应商信息, 成本分析

### 交互逻辑与状态：

#### **工序外协标识**
- **内制工序：** 灰色标签(#8C8C8C)，显示"内制"
- **外协工序：** 橙色标签(#FAAD14)，显示"外协"
- **混合工序：** 蓝色标签(#1890FF)，显示"混合"
- **切换按钮：** 开关组件，支持内制/外协切换

#### **外协类型选择器**
- **完全外协：** 整个工序委托外部完成
- **部分外协：** 工序的某些环节外协
- **临时外协：** 根据产能情况临时外协
- **选择状态：** 单选按钮组，选中项蓝色背景

#### **供应商选择器**
- **主供应商：** 下拉选择，显示供应商名称和评级
- **备选供应商：** 多选列表，最多选择3个
- **供应商信息：** 悬停显示详细信息卡片
- **新增供应商：** 蓝色按钮，打开供应商添加对话框

#### **外协价格配置**
- **单价输入：** 数值输入框，支持小数
- **计价方式：** 下拉选择（按件/按时/按面积/按重量）
- **价格有效期：** 日期选择器，设置价格有效期
- **价格历史：** 显示历史价格变化趋势

#### **交期配置工具**
- **标准交期：** 数值输入，单位天
- **加急交期：** 数值输入，加急情况下的交期
- **交期计算：** 基于工序复杂度自动计算建议交期
- **交期预警：** 设置交期预警阈值

#### **质量要求编辑器**
- **技术标准：** 文本编辑器，输入技术要求
- **质量标准：** 下拉选择预定义的质量等级
- **检验要求：** 复选框组，选择检验项目
- **合格率要求：** 数值输入，设置合格率标准

#### **成本分析面板**
- **外协成本：** 显示外协的总成本
- **内制成本：** 显示内制的预估成本
- **成本对比：** 图表形式显示成本差异
- **成本趋势：** 显示历史成本变化趋势

### 数据校验规则：

#### **外协价格**
- **校验规则：** 必填，数值类型，大于0
- **错误提示文案：** "外协价格必须大于0"

#### **交期设置**
- **校验规则：** 必填，整数类型，大于0
- **错误提示文案：** "交期必须为正整数"

#### **供应商选择**
- **校验规则：** 外协工序必须选择供应商
- **错误提示文案：** "外协工序必须选择供应商"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工序ID (operation_id)**: String, 必填, 关联工艺路线工序
- **外协类型 (outsourcing_type)**: Enum, 必填, [完全外协/部分外协/临时外协]
- **主供应商 (primary_supplier_id)**: String, 必填
- **备选供应商 (backup_suppliers)**: Array, 可选, 供应商ID列表
- **外协价格信息**:
  - **单价 (unit_price)**: Number, 必填, 外协单价
  - **计价方式 (pricing_method)**: Enum, 必填, [按件/按时/按面积/按重量]
  - **价格有效期 (price_valid_until)**: Date, 必填
- **交期信息**:
  - **标准交期 (standard_lead_time)**: Integer, 必填, 单位天
  - **加急交期 (urgent_lead_time)**: Integer, 可选, 单位天

### 展示数据
- **工序基本信息**: 工序号、名称、外协状态、外协类型
- **供应商信息**: 主供应商、备选供应商、供应商评级
- **价格信息**: 当前价格、历史价格、价格趋势
- **交期信息**: 标准交期、加急交期、平均实际交期
- **成本分析**: 外协成本、内制成本、成本差异

### 空状态/零数据
- **无外协工序**: 显示"该工艺路线暂无外协工序"
- **无供应商**: 显示"请先添加外协供应商"
- **无价格历史**: 显示"暂无价格历史数据"

### API接口
- **设置外协工序**: POST /api/operations/{id}/outsourcing
- **更新外协配置**: PUT /api/operations/{id}/outsourcing
- **获取供应商列表**: GET /api/suppliers/by-capability
- **外协成本计算**: POST /api/operations/outsourcing/cost-calculate
- **内制外协切换**: PUT /api/operations/{id}/switch-mode

## 5. 异常与边界处理 (Error & Edge Cases)

### **供应商能力不匹配**
- **提示信息**: "选择的供应商不具备该工序的加工能力"
- **用户操作**: 高亮不匹配的供应商，提供能力匹配的供应商列表

### **外协价格异常**
- **提示信息**: "外协价格异常：超出合理范围"
- **用户操作**: 价格字段标红，显示合理价格范围

### **交期冲突**
- **提示信息**: "外协交期与生产计划冲突"
- **用户操作**: 显示冲突的订单，提供交期调整建议

### **供应商状态异常**
- **提示信息**: "选择的供应商已被禁用或删除"
- **用户操作**: 自动移除异常供应商，提示重新选择

### **成本计算失败**
- **提示信息**: "外协成本计算失败：缺少必要的价格信息"
- **用户操作**: 高亮缺失的价格信息，提供补充入口

### **切换模式限制**
- **提示信息**: "该工序正在生产中，不能切换外协模式"
- **用户操作**: 显示正在进行的生产订单，提供计划切换功能

## 6. 验收标准 (Acceptance Criteria)

- [ ] 用户可以将工序标识为外协或内制
- [ ] 支持完全外协、部分外协、临时外协等类型
- [ ] 外协工序可关联主供应商和备选供应商
- [ ] 支持外协价格配置和历史价格管理
- [ ] 外协交期设置和交期预警功能正常
- [ ] 支持内制和外协模式的灵活切换
- [ ] 外协成本计算准确，支持成本对比分析
- [ ] 供应商能力匹配验证正确
- [ ] 外协工序的质量要求配置完整
- [ ] 外协信息变更记录完整的审计轨迹
- [ ] 外协数据可供采购和生产系统调用
- [ ] 所有操作记录到审计日志
- [ ] 界面支持响应式设计，移动端可正常使用
- [ ] 外协配置响应时间小于1秒
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持键盘导航和无障碍访问
